import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:rupify/models/product_api.dart';
import 'package:rupify/models/order.dart';

class ApiService {
  static const String baseUrl = 'https://katregeneralstores.supremenews.in/api';

  // Fetch products from API
  static Future<List<ProductApi>> fetchProducts() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/get_products.php'));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return (data['products'] as List)
              .map((product) => ProductApi.fromJson(product))
              .toList();
        }
      }
      return [];
    } catch (e) {
      print('Error fetching products: $e');
      return [];
    }
  }

  // Place an order
  static Future<Map<String, dynamic>> placeOrder(Order order, {String? token, String? userId}) async {
    try {
      // Ensure product ID and quantity are valid
      final productId = order.productId;
      final quantity = order.quantity > 0 ? order.quantity : 1; // Ensure quantity is at least 1
      
      // Transform Order model to match API expectations
      final Map<String, dynamic> orderData = {
        'customer_name': order.customerName,
        'phone_primary': order.phonePrimary,
        'phone_alternate': order.phoneAlternate ?? '',
        'address': order.address,
        'notes': '',  // Add any notes from your order model if available
        'products': [
          {
            'id': int.tryParse(productId) ?? 0, // Ensure ID is an integer
            'quantity': quantity
          }
        ]
      };
      
      // Add authentication data if available
      if (token != null) {
        orderData['token'] = token;
      }
      if (userId != null) {
        orderData['user_id'] = userId;
      }
      
      print('Sending order data: ${jsonEncode(orderData)}'); // Log for debugging
      
      final response = await http.post(
        Uri.parse('$baseUrl/place_order.php'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(orderData),
      );
      
      print('Order response: ${response.statusCode} - ${response.body}');
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        return {
          'success': responseData['success'] ?? false,
          'message': responseData['message'] ?? 'Unknown response',
          'data': responseData
        };
      }
      
      return {
        'success': false,
        'message': 'Failed to place order. Status: ${response.statusCode}',
      };
    } catch (e) {
      print('Error placing order: $e');
      return {
        'success': false,
        'message': 'Error: $e',
      };
    }
  }

  // Fetch user orders
  static Future<List<Order>> fetchOrders() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/order_list.php'));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return (data['orders'] as List)
              .map((order) => Order.fromJson(order))
              .toList();
        }
      }
      return [];
    } catch (e) {
      print('Error fetching orders: $e');
      return [];
    }
  }
}
