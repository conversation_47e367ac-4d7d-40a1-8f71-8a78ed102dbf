import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:rupify/models/expense.dart';
import 'package:rupify/models/category_spending.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class ExpenseProvider extends ChangeNotifier {
  List<Expense> _expenses = [];
  double _monthlyBudget = 10000.0;
  
  ExpenseProvider() {
    _loadData();
  }
  
  // Getters
  List<Expense> get expenses => _expenses;
  double get monthlyBudget => _monthlyBudget;
  
  // Get total spent this month
  double get totalSpentThisMonth {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    return _expenses
        .where((expense) => 
            expense.date.isAfter(startOfMonth) && 
            expense.date.isBefore(endOfMonth.add(const Duration(days: 1))))
        .fold(0, (sum, expense) => sum + expense.amount);
  }
  
  // Get remaining budget
  double get remainingBudget => _monthlyBudget - totalSpentThisMonth;
  
  // Get recent expenses (sorted by date, newest first)
  List<Expense> get recentExpenses {
    final sortedExpenses = List<Expense>.from(_expenses);
    sortedExpenses.sort((a, b) => b.date.compareTo(a.date));
    return sortedExpenses.take(5).toList();
  }
  
  // Get top categories by amount
  Map<ExpenseCategory, double> get topCategoryTotals {
    final Map<ExpenseCategory, double> totals = {};
    
    // Get expenses from the current month
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    final monthlyExpenses = _expenses
        .where((expense) => 
            expense.date.isAfter(startOfMonth) && 
            expense.date.isBefore(endOfMonth.add(const Duration(days: 1))))
        .toList();
    
    // Calculate totals by category
    for (var expense in monthlyExpenses) {
      totals[expense.category] = (totals[expense.category] ?? 0) + expense.amount;
    }
    
    return totals;
  }
  
  // Get weekly expenses
  List<Expense> getWeeklyExpenses() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final startDate = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);
    final endDate = startDate.add(const Duration(days: 7));
    
    return _expenses
        .where((expense) => 
            expense.date.isAfter(startDate.subtract(const Duration(days: 1))) && 
            expense.date.isBefore(endDate))
        .toList();
  }
  
  // Get monthly expenses
  List<Expense> getMonthlyExpenses() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    return _expenses
        .where((expense) => 
            expense.date.isAfter(startOfMonth.subtract(const Duration(days: 1))) && 
            expense.date.isBefore(endOfMonth.add(const Duration(days: 1))))
        .toList();
  }
  
  // Get yearly expenses
  List<Expense> getYearlyExpenses() {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final endOfYear = DateTime(now.year, 12, 31);
    
    return _expenses
        .where((expense) => 
            expense.date.isAfter(startOfYear.subtract(const Duration(days: 1))) && 
            expense.date.isBefore(endOfYear.add(const Duration(days: 1))))
        .toList();
  }
  
  // Get category totals for a list of expenses
  Map<ExpenseCategory, double> getCategoryTotals(List<Expense> expenses) {
    final Map<ExpenseCategory, double> totals = {};
    
    for (var expense in expenses) {
      totals[expense.category] = (totals[expense.category] ?? 0) + expense.amount;
    }
    
    return totals;
  }
  
  // Add a new expense
  Future<void> addExpense(Expense expense) async {
    _expenses.add(expense);
    await _saveData();
    notifyListeners();
  }
  
  // Update an existing expense
  Future<void> updateExpense(Expense updatedExpense) async {
    final index = _expenses.indexWhere((e) => e.id == updatedExpense.id);
    if (index != -1) {
      _expenses[index] = updatedExpense;
      await _saveData();
      notifyListeners();
    }
  }
  
  // Delete an expense
  Future<void> deleteExpense(String id) async {
    _expenses.removeWhere((e) => e.id == id);
    await _saveData();
    notifyListeners();
  }
  
  // Set monthly budget
  Future<void> setMonthlyBudget(double budget) async {
    _monthlyBudget = budget;
    await _saveData();
    notifyListeners();
  }
  
  // Refresh data
  Future<void> refreshData() async {
    await _loadData();
    notifyListeners();
  }
  
  // Load data from SharedPreferences
  Future<void> _loadData() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Load monthly budget
    _monthlyBudget = prefs.getDouble('monthlyBudget') ?? 10000.0;
    
    // Load expenses
    final expensesJson = prefs.getStringList('expenses') ?? [];
    _expenses = expensesJson
        .map((json) => Expense.fromJson(jsonDecode(json)))
        .toList();
    
    notifyListeners();
  }
  
  // Save data to SharedPreferences
  Future<void> _saveData() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Save monthly budget
    await prefs.setDouble('monthlyBudget', _monthlyBudget);
    
    // Save expenses
    final expensesJson = _expenses
        .map((expense) => jsonEncode(expense.toJson()))
        .toList();
    
    await prefs.setStringList('expenses', expensesJson);
  }
  
  // Format currency
  String formatCurrency(double amount) {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 2,
    );
    return formatter.format(amount);
  }

  // Get recent expenses with limit
  List<Expense> getRecentExpenses(int limit) {
    final sortedExpenses = List<Expense>.from(_expenses);
    sortedExpenses.sort((a, b) => b.date.compareTo(a.date));
    return sortedExpenses.take(limit).toList();
  }

  // Get category spending data
  List<CategorySpending> getCategorySpending() {
    final Map<ExpenseCategory, double> totals = {};
    
    // Get expenses from the current month
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    final monthlyExpenses = _expenses
        .where((expense) => 
            expense.date.isAfter(startOfMonth) && 
            expense.date.isBefore(endOfMonth.add(const Duration(days: 1))))
        .toList();
    
    // Calculate totals by category
    for (var expense in monthlyExpenses) {
      totals[expense.category] = (totals[expense.category] ?? 0) + expense.amount;
    }
    
    // Convert to list of CategorySpending objects
    return totals.entries
        .map((entry) => CategorySpending(
              category: entry.key,
              amount: entry.value,
            ))
        .toList();
  }

  Future<Map<String, dynamic>> uploadExpensesToServer(String token) async {
    if (token.isEmpty) {
      throw Exception('Authentication token is required');
    }
    
    final baseApiUrl = 'https://katregeneralstores.supremenews.in/api';
    int successCount = 0;
    
    try {
      // Upload each expense
      for (var expense in _expenses) {
        final response = await http.post(
          Uri.parse('$baseApiUrl/users.php?action=upload-expenses'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: json.encode({
            'title': expense.description,
            'description': expense.notes,
            'category': expense.category.displayName,
            'amount': expense.amount,
            'date': expense.date.toIso8601String().split('T')[0], // Format as YYYY-MM-DD
            'lend': expense.isLend ? 1 : 0, // Include lend status
          }),
        );
        
        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['success'] == true) {
            successCount++;
          }
        }
      }
      
      return {
        'success': true,
        'count': successCount,
        'total': _expenses.length,
      };
    } catch (e) {
      throw Exception('Failed to upload expenses: ${e.toString()}');
    }
  }

  Future<Map<String, dynamic>> fetchExpensesFromServer(String token) async {
    if (token.isEmpty) {
      throw Exception('Authentication token is required');
    }
    
    final baseApiUrl = 'https://katregeneralstores.supremenews.in/api';
    
    try {
      final response = await http.get(
        Uri.parse('$baseApiUrl/users.php?action=fetch-expenses'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['success'] == true && data['expenses'] is List) {
          final List<dynamic> serverExpenses = data['expenses'];
          final List<Expense> newExpenses = [];
          
          for (var item in serverExpenses) {
            try {
              // Convert server expense format to app format
              final expense = Expense(
                id: item['id'].toString(),
                amount: double.parse(item['amount'].toString()),
                description: item['title'] ?? '',
                notes: item['description'] ?? '',
                date: DateTime.parse(item['expense_date']),
                category: _getCategoryFromString(item['category'] ?? 'others'),
                isLend: item['lend'] == 1, // Parse lend status from server
              );
              
              newExpenses.add(expense);
            } catch (e) {
              print('Error parsing expense: $e');
            }
          }
          
          // Merge with existing expenses or replace them
          // Here we're replacing for simplicity
          _expenses = newExpenses;
          await _saveData();
          notifyListeners();
          
          return {
            'success': true,
            'count': newExpenses.length,
          };
        } else {
          throw Exception(data['message'] ?? 'Failed to fetch expenses');
        }
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch expenses: ${e.toString()}');
    }
  }

  // Helper method to convert string to ExpenseCategory
  ExpenseCategory _getCategoryFromString(String categoryName) {
    try {
      return ExpenseCategory.values.firstWhere(
        (cat) => cat.displayName.toLowerCase() == categoryName.toLowerCase(),
        orElse: () => ExpenseCategory.others,
      );
    } catch (e) {
      return ExpenseCategory.others;
    }
  }
}
