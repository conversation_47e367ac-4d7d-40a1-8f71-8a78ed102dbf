import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rupify/models/product.dart';

class ProductsProvider extends ChangeNotifier {
  List<Product> _products = [];
  
  List<Product> get products => _products;
  
  ProductsProvider() {
    _loadProducts();
    // Add sample products if none exist
    if (_products.isEmpty) {
      _addSampleProducts();
    }
  }
  
  Future<void> _loadProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsJson = prefs.getStringList('products') ?? [];
      
      _products = productsJson
          .map((json) => Product.fromJson(jsonDecode(json)))
          .toList();
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading products: $e');
      // Reset to empty list if there's an error
      _products = [];
      notifyListeners();
    }
  }
  
  Future<void> _saveProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final productsJson = _products
          .map((product) => jsonEncode(product.toJson()))
          .toList();
      
      await prefs.setStringList('products', productsJson);
    } catch (e) {
      debugPrint('Error saving products: $e');
    }
  }
  
  void _addSampleProducts() {
    _products = [
      Product(
        id: '1',
        name: 'Premium Theme',
        description: 'Unlock a premium app theme with dark mode',
        coinPrice: 10,
        imageUrl: 'assets/images/theme.png',
        category: 'Theme',
        price: '0.00',
        features: [],
      ),
      Product(
        id: '2',
        name: 'Ad-Free Experience',
        description: 'Remove all ads for 30 days',
        coinPrice: 20,
        imageUrl: 'assets/images/ad_free.png',
        category: 'Feature',
        price: '0.00',
        features: [],
      ),
      Product(
        id: '3',
        name: 'Budget Insights',
        description: 'Unlock advanced budget analytics',
        coinPrice: 15,
        imageUrl: 'assets/images/insights.png',
        category: 'Feature',
        price: '0.00',
        features: [],
      ),
    ];
    
    _saveProducts();
    notifyListeners();
  }
  
  Future<void> addProduct(Product product) async {
    _products.add(product);
    await _saveProducts();
    notifyListeners();
  }
}
