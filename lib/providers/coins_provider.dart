import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:rupify/providers/auth_provider.dart';

// Add a global navigator key
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Add this constant at the top of the file, outside the class
const String baseApiUrl = 'https://katregeneralstores.supremenews.in/api';

class CoinsProvider with ChangeNotifier {
  int _coins = 0;
  DateTime? _lastCheckIn;
  int _consecutiveCheckIns = 0;
  int _referralBonus = 4; // Default referral bonus
  String? _referralCode;

  int get coins => _coins;
  DateTime? get lastCheckIn => _lastCheckIn;
  int get consecutiveCheckIns => _consecutiveCheckIns;
  int get referralBonus => _referralBonus;
  String? get referralCode => _referralCode;

  CoinsProvider() {
    _loadData();
    _loadSettings();
  }

  Future<void> _loadData() async {
    final prefs = await SharedPreferences.getInstance();
    _coins = prefs.getInt('coins') ?? 0;
    
    final lastCheckInStr = prefs.getString('lastCheckIn');
    _lastCheckIn = lastCheckInStr != null && lastCheckInStr.isNotEmpty
        ? DateTime.parse(lastCheckInStr)
        : null;
    
    _consecutiveCheckIns = prefs.getInt('consecutiveCheckIns') ?? 0;
    
    // Load referral code
    _referralCode = prefs.getString('referralCode');
    
    // Generate referral code if not exists
    if (_referralCode == null || _referralCode!.isEmpty) {
      await generateReferralCode();
    }
    
    notifyListeners();
  }

  Future<void> _loadSettings() async {
    try {
      final response = await http.get(Uri.parse('https://katregeneralstores.supremenews.in/api/settings.php'));
      
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          _referralBonus = data['referral'] ?? 4;
        }
      }
    } catch (e) {
      // Use default value if API call fails
      _referralBonus = 4;
    }
  }

  Future<void> _saveData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('coins', _coins);
    await prefs.setString('lastCheckIn', _lastCheckIn?.toIso8601String() ?? '');
    await prefs.setInt('consecutiveCheckIns', _consecutiveCheckIns);
    
    // Save referral code
    if (_referralCode != null) {
      await prefs.setString('referralCode', _referralCode!);
    }
  }

  Future<bool> spendCoins(int amount, {String reason = 'Purchase'}) async {
    if (_coins < amount) {
      return false;
    }
    
    // Update locally first for immediate feedback
    _coins -= amount;
    await _saveData();
    notifyListeners();
    
    // Then update on server if user is logged in
    await updateCoinsOnServer(-amount, reason);
    return true;
  }

  Future<void> addCoins(int amount, {String reason = 'Manual addition'}) async {
    // Update locally first for immediate feedback
    _coins += amount;
    await _saveData();
    notifyListeners();
    
    // Then update on server if user is logged in
    await updateCoinsOnServer(amount, reason);
  }

  Future<Map<String, dynamic>> checkIn() async {
    final now = DateTime.now();
    
    // Check if already checked in today
    if (_lastCheckIn != null) {
      final lastCheckInDate = DateTime(_lastCheckIn!.year, _lastCheckIn!.month, _lastCheckIn!.day);
      final today = DateTime(now.year, now.month, now.day);
      
      if (lastCheckInDate.isAtSameMomentAs(today)) {
        return {
          'success': false,
          'message': 'You have already checked in today'
        };
      }
      
      // Check if consecutive
      final yesterday = today.subtract(const Duration(days: 1));
      final lastCheckDay = DateTime(_lastCheckIn!.year, _lastCheckIn!.month, _lastCheckIn!.day);
      
      if (lastCheckDay.isAtSameMomentAs(yesterday)) {
        _consecutiveCheckIns++;
      } else {
        _consecutiveCheckIns = 1;
      }
    } else {
      _consecutiveCheckIns = 1;
    }
    
    // Award coins based on streak
    int coinsEarned = 1; // Base coin for daily check-in
    String message = 'You earned 1 coin for checking in today!';
    
    // 3 consecutive days bonus
    if (_consecutiveCheckIns % 3 == 0) {
      coinsEarned += 3;
      message = 'You earned 4 coins! (1 daily + 3 for 3-day streak)';
    }
    
    // Weekly bonus (7 days)
    if (_consecutiveCheckIns % 7 == 0) {
      coinsEarned += 3;
      message = 'You earned 7 coins! (1 daily + 3 for 3-day streak + 3 for weekly streak)';
    }
    
    _coins += coinsEarned;
    _lastCheckIn = now;
    
    await _saveData();
    notifyListeners();
    
    return {
      'success': true,
      'coinsEarned': coinsEarned,
      'totalCoins': _coins,
      'streak': _consecutiveCheckIns,
      'message': message
    };
  }

  // Process referral
  Future<Map<String, dynamic>> processReferral(String referralCode) async {
    try {
      // Validate referral code
      if (referralCode.isEmpty) {
        return {
          'success': false,
          'message': 'Invalid referral code'
        };
      }
      
      // Get the BuildContext from the navigator key
      final context = navigatorKey.currentContext;
      if (context == null) {
        return {
          'success': false,
          'message': 'Unable to process referral at this time'
        };
      }
      
      // Get the auth provider
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      // Check if user is using their own code
      if (referralCode == authProvider.userId) {
        return {
          'success': false,
          'message': 'You cannot use your own referral code'
        };
      }
      
      // Make API call to process referral
      final response = await http.post(
        Uri.parse('$baseApiUrl/users.php?action=process-referral'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${authProvider.token}'
        },
        body: json.encode({
          'referral_code': referralCode,
          'user_id': authProvider.userId
        }),
      );
      
      final data = json.decode(response.body);
      
      if (data['success']) {
        // Add coins to current user - ensure it's an int
        final coinsAwarded = (data['coins_awarded'] ?? 10) as int;
        _coins += coinsAwarded;
        await _saveData();
        notifyListeners();
        
        return {
          'success': true,
          'message': data['message'] ?? 'Referral processed successfully! You earned $coinsAwarded coins.',
          'coinsAwarded': coinsAwarded
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to process referral'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Error processing referral: ${e.toString()}'
      };
    }
  }

  // Generate referral code if not exists
  Future<String> generateReferralCode() async {
    if (_referralCode == null || _referralCode!.isEmpty) {
      // Generate a random code
      final random = Random();
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      _referralCode = List.generate(8, (index) => chars[random.nextInt(chars.length)]).join();
      
      await _saveData();
      notifyListeners();
    }
    
    return _referralCode!;
  }

  // Sync coins with server
  Future<void> syncCoinsWithServer() async {
    // Get the context safely
    final context = navigatorKey.currentContext;
    if (context == null) {
      debugPrint('Context is null in syncCoinsWithServer');
      return;
    }
    
    // Get the auth provider
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    // Only sync if user is logged in and has a token
    if (!authProvider.isLoggedIn || authProvider.token.isEmpty) {
      debugPrint('User not logged in or token is empty');
      return;
    }
    
    try {
      final response = await http.get(
        Uri.parse('$baseApiUrl/users.php?action=get-user'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${authProvider.token}',
        },
      );
      
      debugPrint('Sync coins response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['user'] != null) {
          final serverCoins = data['user']['coins'] ?? 0;
          debugPrint('Server coins: $serverCoins, Local coins: $_coins');
          
          // Update local coins if different from server
          if (serverCoins != _coins) {
            await setCoins(serverCoins is int ? serverCoins : int.tryParse(serverCoins.toString()) ?? 0);
          }
        }
      }
    } catch (e) {
      // Handle error silently - we'll just use local coins
      debugPrint('Error syncing coins: $e');
    }
  }

  // Update coins on server
  Future<bool> updateCoinsOnServer(int amount, String reason) async {
    try {
      final context = navigatorKey.currentContext;
      if (context == null) return false;
      
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (!authProvider.isLoggedIn) return false;
      
      // Get user details
      final userId = authProvider.userId;
      final token = authProvider.token;
      
      if (token.isEmpty) return false;
      
      // Update coins on server
      final response = await http.post(
        Uri.parse('$baseApiUrl/users.php?action=update-coins'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token'
        },
        body: json.encode({
          'token': token,
          'coins': amount,
          'reason': reason
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          // Update local coins with new balance from server
          _coins = data['new_balance'];
          await _saveData();
          notifyListeners();
          return true;
        }
      }
      
      return false;
    } catch (e) {
      print('Error updating coins on server: $e');
      return false;
    }
  }

  // Set coins directly (used when syncing with server)
  Future<void> setCoins(int amount) async {
    _coins = amount;
    await _saveData();
    notifyListeners();
  }
}
