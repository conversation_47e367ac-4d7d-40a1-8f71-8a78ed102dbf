import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:rupify/models/product_api.dart';

class ApiProductsProvider with ChangeNotifier {
  List<ProductApi> _products = [];
  bool _isLoading = false;
  String _error = '';
  String _currentCategory = 'All';
  String _searchQuery = '';
  String _sortBy = 'featured'; // Options: featured, price_low, price_high, newest

  List<ProductApi> get products => _products;
  bool get isLoading => _isLoading;
  String get error => _error;
  String get currentCategory => _currentCategory;
  String get searchQuery => _searchQuery;
  String get sortBy => _sortBy;

  Future<void> fetchProducts({
    String? search,
    String? categorySlug,
    int? categoryId,
    String? sort,
    int? limit,
  }) async {
    _isLoading = true;
    _error = '';
    
    // Update state variables if provided
    if (search != null) _searchQuery = search;
    if (categorySlug != null) _currentCategory = categorySlug;
    if (sort != null) _sortBy = sort;
    
    notifyListeners();

    try {
      // If search query is provided and not empty, use search_products.php
      final Uri uri;
      
      if (_searchQuery.isNotEmpty) {
        uri = Uri.parse('https://katregeneralstores.supremenews.in/api/search_products.php')
            .replace(queryParameters: {
          'query': _searchQuery,
        });
      } else {
        // Otherwise use get_products.php with filters
        uri = Uri.parse('https://katregeneralstores.supremenews.in/api/get_products.php')
            .replace(queryParameters: {
          if (_currentCategory != 'All') 'category_slug': _currentCategory,
          if (categoryId != null) 'category_id': categoryId.toString(),
          if (_sortBy != 'featured') 'sort': _sortBy,
          if (limit != null) 'limit': limit.toString(),
        });
      }

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        if (data['success'] == true) {
          _products = (data['products'] as List)
              .map((product) => ProductApi.fromJson(product))
              .toList();
        } else {
          _error = data['message'] ?? 'Failed to load products';
        }
      } else {
        _error = 'Server error: ${response.statusCode}';
      }
    } catch (e) {
      _error = 'Error: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void setCategory(String category) {
    if (_currentCategory != category) {
      _currentCategory = category;
      fetchProducts(categorySlug: category == 'All' ? '' : category);
    }
  }

  void setSearchQuery(String query) {
    if (_searchQuery != query) {
      _searchQuery = query;
      // Reset category to 'All' when searching
      _currentCategory = 'All';
      fetchProducts(search: query);
    }
  }

  void setSortBy(String sort) {
    if (_sortBy != sort) {
      _sortBy = sort;
      fetchProducts(sort: sort);
    }
  }

  void clearFilters() {
    _currentCategory = 'All';
    _searchQuery = '';
    _sortBy = 'featured';
    fetchProducts();
  }

  ProductApi? getProductById(String id) {
    try {
      return _products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }
}
