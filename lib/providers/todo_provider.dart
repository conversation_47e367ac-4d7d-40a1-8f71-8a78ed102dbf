import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class TodoItem {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final TimeOfDay? time;
  final bool isCompleted;

  TodoItem({
    required this.id,
    required this.title,
    this.description = '',
    required this.date,
    this.time,
    this.isCompleted = false,
  });

  TodoItem copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? date,
    TimeOfDay? time,
    bool? isCompleted,
  }) {
    return TodoItem(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      time: time ?? this.time,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'time': time != null ? '${time!.hour.toString().padLeft(2, '0')}:${time!.minute.toString().padLeft(2, '0')}' : null,
      'isCompleted': isCompleted,
    };
  }

  factory TodoItem.fromJson(Map<String, dynamic> json) {
    TimeOfDay? todoTime;
    if (json['time'] != null && json['time'].toString().isNotEmpty) {
      final timeParts = json['time'].toString().split(':');
      if (timeParts.length == 2) {
        todoTime = TimeOfDay(
          hour: int.parse(timeParts[0]),
          minute: int.parse(timeParts[1]),
        );
      }
    }
    
    return TodoItem(
      id: json['id'].toString(),
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      date: json['date'] is String 
          ? DateTime.parse(json['date']) 
          : DateTime.fromMillisecondsSinceEpoch(0),
      time: todoTime,
      isCompleted: json['isCompleted'] == true || json['is_completed'] == 1,
    );
  }
}

class TodoProvider with ChangeNotifier {
  List<TodoItem> _todos = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  
  List<TodoItem> get todos => _todos;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  
  TodoProvider() {
    _loadTodos();
  }
  
  Future<void> _loadTodos() async {
    final prefs = await SharedPreferences.getInstance();
    final todosJson = prefs.getStringList('todos') ?? [];
    
    _todos = todosJson
        .map((json) => TodoItem.fromJson(jsonDecode(json)))
        .toList();
    
    notifyListeners();
  }
  
  Future<void> _saveTodos() async {
    final prefs = await SharedPreferences.getInstance();
    final todosJson = _todos
        .map((todo) => jsonEncode(todo.toJson()))
        .toList();
    
    await prefs.setStringList('todos', todosJson);
  }
  
  void addTodo(TodoItem todo) {
    _todos.add(todo);
    _saveTodos();
    notifyListeners();
  }
  
  void updateTodo(TodoItem todo) {
    final index = _todos.indexWhere((t) => t.id == todo.id);
    if (index >= 0) {
      _todos[index] = todo;
      _saveTodos();
      notifyListeners();
    }
  }
  
  void deleteTodo(String id) {
    _todos.removeWhere((todo) => todo.id == id);
    _saveTodos();
    notifyListeners();
  }
  
  void toggleTodoCompletion(String id) {
    final index = _todos.indexWhere((t) => t.id == id);
    if (index >= 0) {
      final todo = _todos[index];
      _todos[index] = todo.copyWith(isCompleted: !todo.isCompleted);
      _saveTodos();
      notifyListeners();
    }
  }
  
  List<TodoItem> getTodosForDate(DateTime date) {
    return _todos.where((todo) => 
      todo.date.year == date.year && 
      todo.date.month == date.month && 
      todo.date.day == date.day
    ).toList();
  }
  
  // API methods
  Future<Map<String, dynamic>> uploadTodosToServer(String token) async {
    if (token.isEmpty) {
      throw Exception('Authentication token is required');
    }
    
    _isLoading = true;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
    
    final baseApiUrl = 'https://katregeneralstores.supremenews.in/api';
    int successCount = 0;
    
    try {
      // Upload each todo
      for (var todo in _todos) {
        final response = await http.post(
          Uri.parse('$baseApiUrl/users.php?action=upload-todos'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: json.encode({
            'title': todo.title,
            'description': todo.description,
            'date': todo.date.toIso8601String().split('T')[0], // Format as YYYY-MM-DD
            'time': todo.time != null 
                ? '${todo.time!.hour.toString().padLeft(2, '0')}:${todo.time!.minute.toString().padLeft(2, '0')}' 
                : null,
            'is_completed': todo.isCompleted ? 1 : 0,
          }),
        );
        
        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['success'] == true) {
            successCount++;
          }
        }
      }
      
      _isLoading = false;
      notifyListeners();
      
      return {
        'success': true,
        'count': successCount,
        'total': _todos.length,
      };
    } catch (e) {
      _isLoading = false;
      _hasError = true;
      _errorMessage = e.toString();
      notifyListeners();
      
      throw Exception('Failed to upload todos: ${e.toString()}');
    }
  }
  
  Future<Map<String, dynamic>> fetchTodosFromServer(String token) async {
    if (token.isEmpty) {
      throw Exception('Authentication token is required');
    }
    
    _isLoading = true;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
    
    final baseApiUrl = 'https://katregeneralstores.supremenews.in/api';
    
    try {
      final response = await http.get(
        Uri.parse('$baseApiUrl/users.php?action=fetch-todos'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['success'] == true) {
          final List<dynamic> todosData = data['todos'] ?? [];
          final List<TodoItem> serverTodos = [];
          
          for (var item in todosData) {
            TimeOfDay? todoTime;
            if (item['todo_time'] != null && item['todo_time'].toString().isNotEmpty) {
              final timeParts = item['todo_time'].toString().split(':');
              if (timeParts.length >= 2) {
                todoTime = TimeOfDay(
                  hour: int.parse(timeParts[0]),
                  minute: int.parse(timeParts[1]),
                );
              }
            }
            
            final todo = TodoItem(
              id: item['id'].toString(),
              title: item['title'] ?? '',
              description: item['description'] ?? '',
              date: DateTime.parse(item['todo_date']),
              time: todoTime,
              isCompleted: item['is_completed'] == 1,
            );
            
            serverTodos.add(todo);
          }
          
          // Merge with local todos
          _todos = [...serverTodos];
          _saveTodos();
          
          _isLoading = false;
          notifyListeners();
          
          return {
            'success': true,
            'count': serverTodos.length,
          };
        } else {
          _isLoading = false;
          _hasError = true;
          _errorMessage = data['message'] ?? 'Unknown error';
          notifyListeners();
          
          return {
            'success': false,
            'message': _errorMessage,
          };
        }
      } else {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Server returned status code ${response.statusCode}';
        notifyListeners();
        
        return {
            'success': false,
            'message': _errorMessage,
        };
      }
    } catch (e) {
      _isLoading = false;
      _hasError = true;
      _errorMessage = e.toString();
      notifyListeners();
      
      throw Exception('Failed to fetch todos: ${e.toString()}');
    }
  }
}