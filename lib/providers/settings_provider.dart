import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider extends ChangeNotifier {
  bool _isHindi = false;
  bool _isDailyReminderEnabled = false;
  TimeOfDay _reminderTime = const TimeOfDay(hour: 20, minute: 0);
  ThemeMode _themeMode = ThemeMode.system;
  
  SettingsProvider() {
    _loadSettings();
  }
  
  // Getters
  bool get isHindi => _isHindi;
  bool get isDailyReminderEnabled => _isDailyReminderEnabled;
  TimeOfDay get reminderTime => _reminderTime;
  ThemeMode get themeMode => _themeMode;
  
  // Toggle language between English and Hindi
  Future<void> toggleLanguage() async {
    _isHindi = !_isHindi;
    await _saveSettings();
    notifyListeners();
  }
  
  // Set daily reminder enabled/disabled (keep this for UI purposes)
  Future<void> setDailyReminder(bool enabled) async {
    _isDailyReminderEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }
  
  // Set reminder time (keep this for UI purposes)
  Future<void> setReminderTime(TimeOfDay time) async {
    _reminderTime = time;
    await _saveSettings();
    notifyListeners();
  }
  
  // Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    await _saveSettings();
    notifyListeners();
  }
  
  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    _isHindi = prefs.getBool('isHindi') ?? false;
    _isDailyReminderEnabled = prefs.getBool('isDailyReminderEnabled') ?? false;
    
    final reminderHour = prefs.getInt('reminderHour') ?? 20;
    final reminderMinute = prefs.getInt('reminderMinute') ?? 0;
    _reminderTime = TimeOfDay(hour: reminderHour, minute: reminderMinute);
    
    final themeModeIndex = prefs.getInt('themeMode') ?? 0;
    _themeMode = ThemeMode.values[themeModeIndex];
    
    notifyListeners();
  }
  
  // Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setBool('isHindi', _isHindi);
    await prefs.setBool('isDailyReminderEnabled', _isDailyReminderEnabled);
    await prefs.setInt('reminderHour', _reminderTime.hour);
    await prefs.setInt('reminderMinute', _reminderTime.minute);
    await prefs.setInt('themeMode', _themeMode.index);
  }
}
