import 'dart:convert';
import 'dart:math'; // Add this import for Random
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:rupify/models/order.dart';
import 'package:rupify/services/api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OrdersProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  String _error = '';
  
  List<Order> get orders => [..._orders];
  bool get isLoading => _isLoading;
  String get error => _error;
  
  // Add the missing loadOrders method
  Future<void> loadOrders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = prefs.getString('orders');
      
      if (ordersJson != null && ordersJson.isNotEmpty) {
        final List<dynamic> decodedData = json.decode(ordersJson);
        _orders = decodedData.map((item) => Order.fromJson(item)).toList();
      }
    } catch (e) {
      _error = e.toString();
      throw e;
    }
  }
  
  Future<void> fetchOrders() async {
    _isLoading = true;
    _error = '';
    notifyListeners();
    
    try {
      await loadOrders();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }
  
  Future<void> _saveOrders() async {
    final prefs = await SharedPreferences.getInstance();
    final ordersJson = json.encode(_orders.map((order) => order.toJson()).toList());
    await prefs.setString('orders', ordersJson);
  }
  
  Future<Map<String, dynamic>> placeOrder(Order order, {String? token, String? userId}) async {
    try {
      // Call API to place order with authentication
      final result = await ApiService.placeOrder(order, token: token, userId: userId);
      
      if (result['success']) {
        // Update order with server data if available
        if (result['data'] != null && result['data']['order'] != null) {
          final orderData = result['data']['order'];
          // Update order with server-generated ID and order number
          order = Order(
            id: orderData['id']?.toString() ?? order.id,
            productId: order.productId,
            productName: order.productName,
            totalAmount: order.totalAmount,
            quantity: order.quantity,
            coinsUsed: order.coinsUsed,
            customerName: order.customerName,
            phonePrimary: order.phonePrimary,
            phoneAlternate: order.phoneAlternate,
            address: order.address,
            date: order.date,
            status: 'pending',
          );
        }
        
        // Add order to local storage
        _orders.add(order);
        await _saveOrders();
        notifyListeners();
        
        return {
          'success': true,
          'message': result['message'] ?? 'Order placed successfully',
          'orderId': order.id,
          'data': result['data']
        };
      } else {
        return {
          'success': false,
          'message': result['message'] ?? 'Failed to place order'
        };
      }
    } catch (e) {
      print('Error in placeOrder: $e');
      return {
        'success': false,
        'message': 'Failed to place order: ${e.toString()}'
      };
    }
  }
  
  Future<List<Order>> fetchUserOrders({String? token}) async {
    _isLoading = true;
    _error = '';
    notifyListeners();
    
    try {
      if (token == null || token.isEmpty) {
        // If no token, return local orders
        await loadOrders();
        _isLoading = false;
        notifyListeners();
        return _orders;
      }
      
      // Fetch orders from API using token
      final response = await http.get(
        Uri.parse('https://katregeneralstores.supremenews.in/api/users.php?action=my-orders'),
        headers: {
          'Authorization': 'Bearer $token'
        },
      );
      
      print('Orders response: ${response.statusCode} - ${response.body}');
      
      if (response.statusCode == 200) {
        try {
          final data = json.decode(response.body);
          
          if (data['success'] == true) {
            // Check if orders key exists and is a list
            if (data.containsKey('orders') && data['orders'] is List) {
              final List<dynamic> ordersData = data['orders'];
              _orders = [];
              
              for (var orderData in ordersData) {
                try {
                  // Ensure order ID is a non-empty string
                  if (orderData['id'] == null || orderData['id'].toString().isEmpty) {
                    orderData['id'] = 'ORDER-${DateTime.now().millisecondsSinceEpoch}-${Random().nextInt(10000)}';
                  }
                  
                  // Ensure coins_used is properly set
                  if (orderData['coins_used'] == null || orderData['coins_used'].toString().isEmpty) {
                    // Calculate from coin_price * quantity if available
                    if (orderData['coin_price'] != null && orderData['quantity'] != null) {
                      int coinPrice = int.tryParse(orderData['coin_price'].toString()) ?? 0;
                      int quantity = int.tryParse(orderData['quantity'].toString()) ?? 0;
                      orderData['coins_used'] = coinPrice * quantity;
                    } else {
                      orderData['coins_used'] = 0;
                    }
                  }
                  
                  final order = Order.fromJson(orderData);
                  _orders.add(order);
                } catch (e) {
                  print('Error parsing order: $e');
                  // Skip invalid orders but continue processing
                }
              }
              
              await _saveOrders();
            } else {
              // If orders key is missing or not a list, use empty list
              _orders = [];
              print('No orders found in response or invalid format');
            }
          } else {
            _error = data['message'] ?? 'Failed to fetch orders';
            // Still load local orders as fallback
            await loadOrders();
          }
        } catch (e) {
          print('Error parsing response: $e');
          _error = 'Error parsing server response';
          // Load local orders as fallback
          await loadOrders();
        }
      } else {
        _error = 'Server error: ${response.statusCode}';
        // Load local orders as fallback
        await loadOrders();
      }
    } catch (e) {
      print('Network error: $e');
      _error = 'Network error: ${e.toString()}';
      // Load local orders as fallback
      await loadOrders();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
    
    return _orders;
  }
  
  Future<Order?> getOrderById(String orderId) async {
    await loadOrders();
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> fetchOrderProductDetails(String orderId) async {
    try {
      final token = await _getToken();
      if (token == null || token.isEmpty) {
        throw Exception('User not authenticated');
      }
      
      final response = await http.get(
        Uri.parse('https://katregeneralstores.supremenews.in/api/users.php?action=order-product-details&order_id=$orderId'),
        headers: {
          'Authorization': 'Bearer $token'
        },
      );

      final data = json.decode(response.body);
      
      if (data['success']) {
        return List<Map<String, dynamic>>.from(data['products']);
      } else {
        throw Exception(data['message'] ?? 'Failed to fetch product details');
      }
    } catch (e) {
      print('Error in fetchOrderProductDetails: $e');
      return [];
    }
  }

  // Helper method to get token
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }
}
