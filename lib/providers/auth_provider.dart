import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rupify/models/user.dart'; // Import the User model
import 'dart:convert';
import 'package:rupify/providers/coins_provider.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http; // Add this import

class AuthProvider extends ChangeNotifier {
  bool _isLoggedIn = false;
  User? _currentUser;
  String? _token;
  
  bool get isLoggedIn => _isLoggedIn;
  User? get currentUser => _currentUser;
  String? get userId => _currentUser?.id;
  String? get username => _currentUser?.name;
  String? get userPhone => _currentUser?.phone;
  String? get userAddress => _currentUser?.address;
  String get token => _token ?? '';

  bool get isUserDataValid => _isLoggedIn && _currentUser != null && _token != null;
  
  // Add a safe method to check if user is logged in with valid data
  bool get hasValidUserData => _isLoggedIn && _currentUser != null;
  
  AuthProvider() {
    _loadAuthState();
  }
  
  Future<void> _loadAuthState() async {
    final prefs = await SharedPreferences.getInstance();
    _isLoggedIn = prefs.getBool('isLoggedIn') ?? false;
    _token = prefs.getString('token');
    
    // Try to load the complete user object first
    final userJson = prefs.getString('currentUser');
    if (userJson != null) {
      try {
        _currentUser = User.fromJson(json.decode(userJson));
      } catch (e) {
        // If there's an error parsing the JSON, fall back to individual fields
        final userId = prefs.getString('userId');
        final userName = prefs.getString('userName');
        final userEmail = prefs.getString('userEmail');
        final userPhone = prefs.getString('userPhone');
        final userAddress = prefs.getString('userAddress');
        final referralCode = prefs.getString('referralCode');
        
        if (userId != null && userName != null) {
          _currentUser = User(
            id: userId,
            name: userName,
            email: userEmail ?? '',
            phone: userPhone,
            address: userAddress,
            token: _token,
            referralCode: referralCode,
          );
        }
      }
    } else {
      // Fall back to individual fields if no complete user object is found
      final userId = prefs.getString('userId');
      final userName = prefs.getString('userName');
      final userEmail = prefs.getString('userEmail');
      final userPhone = prefs.getString('userPhone');
      final userAddress = prefs.getString('userAddress');
      final referralCode = prefs.getString('referralCode');
      
      if (userId != null && userName != null) {
        _currentUser = User(
          id: userId,
          name: userName,
          email: userEmail ?? '',
          phone: userPhone,
          address: userAddress,
          token: _token,
          referralCode: referralCode,
        );
      }
    }
    
    notifyListeners();
  }
  
  Future<void> login(
    String userId,
    String phone, {
    String? token,
    String? name,
    String? email,
    String? referralCode,
    dynamic coins,
  }) async {
    if (userId.isEmpty) {
      throw Exception('User ID cannot be empty');
    }
    
    _isLoggedIn = true;
    _token = token;
    
    // Create user object - ensure userId is a string
    _currentUser = User(
      id: userId.toString(), // Convert to string to ensure it's always a string
      name: name ?? 'User',
      email: email ?? '',
      phone: phone,
      token: token,
      referralCode: referralCode,
    );
    
    // Save to shared preferences
    final prefs = await SharedPreferences.getInstance();
    prefs.setBool('isLoggedIn', true);
    prefs.setString('userId', userId.toString()); // Ensure userId is saved as string
    prefs.setString('userName', _currentUser!.name);
    prefs.setString('userPhone', phone);
    if (email != null) prefs.setString('userEmail', email);
    if (token != null) prefs.setString('token', token);
    if (referralCode != null) prefs.setString('referralCode', referralCode);
    
    // Save the entire user object as JSON for easier retrieval
    prefs.setString('currentUser', json.encode(_currentUser!.toJson()));
    
    // Update coins if provided
    if (coins != null) {
      int coinsValue = coins is int ? coins : int.tryParse(coins.toString()) ?? 0;
      await setCoins(coinsValue);
    }
    
    notifyListeners();
  }

  // Add a method to handle direct API login
  Future<void> loginWithCredentials(String identifier, String password) async {
    try {
      var headers = {'Content-Type': 'application/json'};
      
      var request = http.Request(
        'POST',
        Uri.parse('https://katregeneralstores.supremenews.in/api/users.php?action=login'),
      );
      
      request.body = json.encode({
        'identifier': identifier,
        'password': password,
      });
      
      request.headers.addAll(headers);
      
      http.StreamedResponse response = await request.send();
      
      // Get response as string
      String responseBody = await response.stream.bytesToString();
      
      if (response.statusCode == 200) {
        // Check if response is not empty
        if (responseBody.isNotEmpty) {
          // Parse JSON
          Map<String, dynamic> data = json.decode(responseBody);
          
          if (data['success'] == true) {
            // Login the user - ensure user_id is converted to string
            await login(
              data['user_id']?.toString() ?? '', // Add null check
              data['phone'] ?? identifier,
              token: data['token'],
              name: data['name'],
              email: data['email'],
              referralCode: data['referral_code'],
              coins: data['coins'],
            );
            return;
          } else {
            throw Exception(data['message'] ?? 'Login failed');
          }
        } else {
          throw Exception('Server returned empty response');
        }
      } else {
        throw Exception('Server error: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('Login error: $e');
    }
  }
  
  // Update user from JSON response
  Future<void> updateUserFromJson(Map<String, dynamic> json) async {
    if (json['success'] == true) {
      final userId = json['user_id']?.toString() ?? ''; // Convert to string
      final token = json['token'];
      final referralCode = json['referral_code'];
      
      if (_currentUser != null) {
        _currentUser = _currentUser!.copyWith(
          token: token,
          referralCode: referralCode,
        );
      } else {
        _currentUser = User(
          id: userId,
          name: json['name'] ?? 'User',
          email: json['email'] ?? '',
          phone: json['phone'],
          token: token,
          referralCode: referralCode,
        );
      }
      
      _isLoggedIn = true;
      _token = token;
      
      // Save to shared preferences
      final prefs = await SharedPreferences.getInstance();
      prefs.setBool('isLoggedIn', true);
      prefs.setString('userId', userId);
      if (token != null) prefs.setString('token', token);
      
      notifyListeners();
    }
  }
  
  Future<void> logout() async {
    _isLoggedIn = false;
    _currentUser = null;
    _token = null;
    notifyListeners();
    
    // Clear from shared preferences
    final prefs = await SharedPreferences.getInstance();
    prefs.remove('isLoggedIn');
    prefs.remove('userId');
    prefs.remove('userName');
    prefs.remove('userEmail');
    prefs.remove('userPhone');
    prefs.remove('userAddress');
    prefs.remove('token');
  }

  void updateUserProfile({String? phone, String? address, String? name}) {
    if (_currentUser != null) {
      _currentUser = _currentUser!.copyWith(
        phone: phone ?? _currentUser!.phone,
        address: address ?? _currentUser!.address,
        name: name ?? _currentUser!.name,
      );
      
      // Save to shared preferences
      SharedPreferences.getInstance().then((prefs) {
        if (phone != null) prefs.setString('userPhone', phone);
        if (address != null) prefs.setString('userAddress', address);
        if (name != null) prefs.setString('userName', name);
        
        // Update the complete user object
        prefs.setString('currentUser', json.encode(_currentUser!.toJson()));
      });
      
      notifyListeners();
    }
  }

  Future<void> setCoins(int coins) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('coins', coins);
  }
}
