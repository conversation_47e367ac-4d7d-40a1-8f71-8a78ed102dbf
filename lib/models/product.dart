import 'dart:convert';

class Product {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final int coinPrice;
  final String price;
  final List<String> features;
  final String? redemptionInstructions;
  final String category;
  
  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.coinPrice,
    required this.price,
    this.features = const [],
    this.redemptionInstructions,
    this.category = 'Other',
  });
  
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      imageUrl: json['image_url'],
      coinPrice: json['coin_price'],
      price: json['price'],
      features: List<String>.from(json['features'] ?? []),
      redemptionInstructions: json['redemption_instructions'],
      category: json['category'] ?? 'Other',
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'coin_price': coinPrice,
      'price': price,
      'features': features,
      'redemption_instructions': redemptionInstructions,
      'category': category,
    };
  }
}
