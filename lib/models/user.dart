class User {
  final String id;
  final String name;
  final String? email;
  final String? phone;
  final String? address;
  final String? token;
  final String? referralCode;
  final int coins;

  User({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.address,
    this.token,
    this.referralCode,
    this.coins = 0,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: (json['id'] ?? json['user_id'] ?? '').toString(),
      name: json['name'] ?? 'User',
      email: json['email'],
      phone: json['phone'],
      address: json['address'],
      token: json['token'],
      referralCode: json['referral_code'],
      coins: json['coins'] is int 
          ? json['coins'] 
          : int.tryParse(json['coins']?.toString() ?? '0') ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'token': token,
      'referral_code': referralCode,
      'coins': coins,
    };
  }

  // Safe copyWith method
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? token,
    String? referralCode,
    int? coins,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      token: token ?? this.token,
      referralCode: referralCode ?? this.referralCode,
      coins: coins ?? this.coins,
    );
  }
}
