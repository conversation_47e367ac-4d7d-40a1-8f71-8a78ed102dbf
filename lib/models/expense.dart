import 'package:flutter/material.dart';

class Expense {
  final String id;
  final double amount;
  final String description;
  final DateTime date;
  final ExpenseCategory category;
  final String notes;
  final bool isLend;  // Add isLend field

  Expense({
    required this.id,
    required this.amount,
    required this.description,
    required this.date,
    required this.category,
    this.notes = '',
    this.isLend = false,  // Default to false
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'description': description,
      'date': date.toIso8601String(),
      'category': category.name,
      'notes': notes,
      'isLend': isLend,  // Include isLend in JSON
    };
  }

  factory Expense.fromJson(Map<String, dynamic> json) {
    return Expense(
      id: json['id'],
      amount: json['amount'],
      description: json['description'],
      date: DateTime.parse(json['date']),
      category: ExpenseCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => ExpenseCategory.others,
      ),
      notes: json['notes'] ?? '',
      isLend: json['isLend'] ?? false,  // Parse isLend from JSON
    );
  }
}

enum ExpenseCategory {
  health,
  grocery,
  home,
  transport,
  communication,
  gifts,
  education,
  entertainment,
  bills,
  others,
}

extension ExpenseCategoryExtension on ExpenseCategory {
  String get displayName {
    switch (this) {
      case ExpenseCategory.health: return 'Health';
      case ExpenseCategory.grocery: return 'Grocery';
      case ExpenseCategory.home: return 'Home';
      case ExpenseCategory.transport: return 'Transport';
      case ExpenseCategory.communication: return 'Communication';
      case ExpenseCategory.gifts: return 'Gifts';
      case ExpenseCategory.education: return 'Education';
      case ExpenseCategory.entertainment: return 'Entertainment';
      case ExpenseCategory.bills: return 'Bills';
      case ExpenseCategory.others: return 'Others';
    }
  }

  String get hindiName {
    switch (this) {
      case ExpenseCategory.health: return 'स्वास्थ्य';
      case ExpenseCategory.grocery: return 'किराना';
      case ExpenseCategory.home: return 'घर';
      case ExpenseCategory.transport: return 'यातायात';
      case ExpenseCategory.communication: return 'संचार';
      case ExpenseCategory.gifts: return 'उपहार';
      case ExpenseCategory.education: return 'शिक्षा';
      case ExpenseCategory.entertainment: return 'मनोरंजन';
      case ExpenseCategory.bills: return 'बिल';
      case ExpenseCategory.others: return 'अन्य';
    }
  }

  IconData get icon {
    switch (this) {
      case ExpenseCategory.health: return Icons.medical_services;
      case ExpenseCategory.grocery: return Icons.shopping_cart;
      case ExpenseCategory.home: return Icons.home;
      case ExpenseCategory.transport: return Icons.directions_bus;
      case ExpenseCategory.communication: return Icons.phone;
      case ExpenseCategory.gifts: return Icons.card_giftcard;
      case ExpenseCategory.education: return Icons.school;
      case ExpenseCategory.entertainment: return Icons.movie;
      case ExpenseCategory.bills: return Icons.receipt;
      case ExpenseCategory.others: return Icons.more_horiz;
    }
  }

  Color get color {
    switch (this) {
      case ExpenseCategory.health: return Colors.red;
      case ExpenseCategory.grocery: return Colors.green;
      case ExpenseCategory.home: return Colors.brown;
      case ExpenseCategory.transport: return Colors.blue;
      case ExpenseCategory.communication: return Colors.orange;
      case ExpenseCategory.gifts: return Colors.purple;
      case ExpenseCategory.education: return Colors.indigo;
      case ExpenseCategory.entertainment: return Colors.pink;
      case ExpenseCategory.bills: return Colors.teal;
      case ExpenseCategory.others: return Colors.grey;
    }
  }
}
