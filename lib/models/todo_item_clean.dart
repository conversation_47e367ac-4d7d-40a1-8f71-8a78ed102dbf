import 'package:flutter/material.dart';

class TodoItem {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final TimeOfDay time;
  final bool isCompleted;

  const TodoItem({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.time,
    required this.isCompleted,
  });

  TodoItem copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? date,
    TimeOfDay? time,
    bool? isCompleted,
  }) {
    return TodoItem(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      time: time ?? this.time,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'date': date.millisecondsSinceEpoch,
      'time': {'hour': time.hour, 'minute': time.minute},
      'isCompleted': isCompleted,
    };
  }
  
  factory TodoItem.fromJson(Map<String, dynamic> json) {
    return TodoItem(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      date: DateTime.fromMillisecondsSinceEpoch(json['date'] as int),
      time: TimeOfDay(
        hour: (json['time'] as Map<String, dynamic>)['hour'] as int,
        minute: (json['time'] as Map<String, dynamic>)['minute'] as int,
      ),
      isCompleted: json['isCompleted'] as bool,
    );
  }
}
