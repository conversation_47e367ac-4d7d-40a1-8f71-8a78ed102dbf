import 'dart:math';

class Order {
  final String id;
  final String productId;
  final String productName;
  final String? productImage;
  final String? productDescription;
  final double totalAmount;
  final int quantity;
  final int coinsUsed;
  final String customerName;
  final String phonePrimary;
  final String? phoneAlternate;
  final String address;
  final String date;
  final String status;

  Order({
    required this.id,
    required this.productId,
    required this.productName,
    this.productImage,
    this.productDescription,
    required this.totalAmount,
    required this.quantity,
    required this.coinsUsed,
    required this.customerName,
    required this.phonePrimary,
    this.phoneAlternate,
    required this.address,
    required this.date,
    required this.status,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    try {
      // Generate a unique ID if none is provided
      String id = json['id']?.toString() ?? '';
      if (id.isEmpty) {
        id = 'ORDER-${DateTime.now().millisecondsSinceEpoch}-${Random().nextInt(10000)}';
      }
      
      return Order(
        id: id,
        productId: json['product_id']?.toString() ?? '',
        productName: json['product_name'] ?? 'Unknown Product',
        productImage: json['product_image'],
        productDescription: json['product_description'],
        totalAmount: double.tryParse(json['total_amount']?.toString() ?? '0') ?? 0.0,
        quantity: int.tryParse(json['quantity']?.toString() ?? '0') ?? 0,
        coinsUsed: int.tryParse(json['coins_used']?.toString() ?? '0') ?? 0,
        customerName: json['customer_name'] ?? '',
        phonePrimary: json['phone_primary'] ?? '',
        phoneAlternate: json['phone_alternate'],
        address: json['address'] ?? '',
        date: json['date'] ?? DateTime.now().toIso8601String(),
        status: json['status'] ?? 'pending',
      );
    } catch (e) {
      print('Error creating Order from JSON: $e');
      // Return a default order object instead of crashing
      return Order(
        id: 'ERROR-${DateTime.now().millisecondsSinceEpoch}',
        productId: '',
        productName: 'Error Loading Product',
        productImage: null,
        productDescription: null,
        totalAmount: 0.0,
        quantity: 0,
        coinsUsed: 0,
        customerName: '',
        phonePrimary: '',
        phoneAlternate: null,
        address: '',
        date: DateTime.now().toIso8601String(),
        status: 'error',
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'product_name': productName,
      'product_image': productImage,
      'product_description': productDescription,
      'total_amount': totalAmount,
      'quantity': quantity,
      'coins_used': coinsUsed,
      'customer_name': customerName,
      'phone_primary': phonePrimary,
      'phone_alternate': phoneAlternate,
      'address': address,
      'date': date,
      'status': status,
    };
  }
}
