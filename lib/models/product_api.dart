import 'dart:convert';

class ProductApi {
  final String id;
  final String name;
  final String price;
  final int coinPrice;
  final String category;
  final String description;
  final String image;
  final List<String> additionalImages;
  final Map<String, String> specifications;
  final bool inStock;
  final String? warranty;

  ProductApi({
    required this.id,
    required this.name,
    required this.price,
    required this.coinPrice,
    required this.category,
    required this.description,
    required this.image,
    required this.additionalImages,
    required this.specifications,
    required this.inStock,
    this.warranty,
  });

  factory ProductApi.fromJson(Map<String, dynamic> json) {
    // Handle additional images
    List<String> additionalImages = [];
    if (json['additional_images'] != null) {
      if (json['additional_images'] is List) {
        additionalImages = List<String>.from(json['additional_images']);
      } else if (json['additional_images'] is String) {
        // Handle case where additional_images might be a comma-separated string
        final imagesStr = json['additional_images'] as String;
        if (imagesStr.isNotEmpty) {
          additionalImages = imagesStr.split(',').map((e) => e.trim()).toList();
        }
      }
    }

    // Handle specifications
    Map<String, String> specifications = {};
    if (json['specifications'] != null) {
      if (json['specifications'] is Map) {
        specifications = Map<String, String>.from(json['specifications']);
      } else if (json['specifications'] is String) {
        // Try to parse JSON string if it's provided as a string
        try {
          final Map<String, dynamic> parsedSpecs = jsonDecode(json['specifications']);
          specifications = parsedSpecs.map((key, value) => 
            MapEntry(key, value.toString()));
        } catch (e) {
          // If parsing fails, leave as empty map
          print('Failed to parse specifications: $e');
        }
      }
    }

    return ProductApi(
      id: json['id'].toString(),
      name: json['name'] ?? '',
      price: json['price']?.toString() ?? '0',
      coinPrice: int.tryParse(json['coin_price']?.toString() ?? '0') ?? 0,
      category: json['category'] ?? 'General',
      description: json['description'] ?? '',
      image: json['image'] ?? '',
      additionalImages: additionalImages,
      specifications: specifications,
      inStock: json['in_stock'] == true || json['in_stock'] == 1 || json['in_stock'] == '1',
      warranty: json['warranty']?.toString(),
    );
  }

  get features => null;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'coin_price': coinPrice,
      'category': category,
      'description': description,
      'image': image,
      'additional_images': additionalImages,
      'specifications': specifications,
      'in_stock': inStock,
    };
  }
}
