import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rupify/models/expense.dart';
import 'package:rupify/providers/expense_provider.dart';
import 'package:rupify/providers/settings_provider.dart';

class TopCategories extends StatelessWidget {
  final Map<ExpenseCategory, double> categories;

  const TopCategories({
    super.key,
    required this.categories,
  });

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    if (categories.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(
            child: Text(
              isHindi ? 'कोई श्रेणी डेटा नहीं' : 'No category data',
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
          ),
        ),
      );
    }
    
    // Sort categories by amount (highest first)
    final sortedCategories = categories.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: sortedCategories.length > 5 ? 5 : sortedCategories.length,
        itemBuilder: (context, index) {
          final entry = sortedCategories[index];
          final category = entry.key;
          final amount = entry.value;
          
          return Container(
            width: 120,
            margin: const EdgeInsets.only(right: 12),
            child: Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircleAvatar(
                      backgroundColor: category.color.withOpacity(0.2),
                      child: Icon(
                        category.icon,
                        color: category.color,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isHindi ? category.hindiName : category.displayName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      expenseProvider.formatCurrency(amount),
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}