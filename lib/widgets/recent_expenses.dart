import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rupify/models/expense.dart';
import 'package:rupify/providers/expense_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/screens/expense_detail_screen.dart';
import 'package:rupify/screens/expense_list_screen.dart';
import 'package:rupify/widgets/expense_card.dart';
import 'package:google_fonts/google_fonts.dart';

class RecentExpenses extends StatelessWidget {
  const RecentExpenses({super.key});

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;

    // Get recent expenses (last 5)
    final recentExpenses = expenseProvider.getRecentExpenses(5);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  isHindi ? 'हाल के खर्च' : 'Recent Expenses',
                  style: GoogleFonts.montserrat(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to expenses tab
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(
                        builder: (_) => const ExpenseListScreen(),
                      ),
                    );
                  },
                  child: Text(
                    isHindi ? 'सभी देखें' : 'View All',
                    style: GoogleFonts.montserrat(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (recentExpenses.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.receipt_long_outlined,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        isHindi ? 'कोई खर्च नहीं मिला' : 'No expenses found',
                        style: GoogleFonts.lato(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: () {
                          // Navigate to add expense screen
                          Navigator.pushNamed(context, '/add_expense');
                        },
                        icon: const Icon(Icons.add),
                        label: Text(
                          isHindi ? 'खर्च जोड़ें' : 'Add Expense',
                          style: GoogleFonts.montserrat(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: recentExpenses.length,
                itemBuilder: (context, index) {
                  final expense = recentExpenses[index];
                  return ExpenseCard(
                    expense: expense,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) =>
                                  ExpenseDetailScreen(expense: expense),
                        ),
                      );
                    },
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}
