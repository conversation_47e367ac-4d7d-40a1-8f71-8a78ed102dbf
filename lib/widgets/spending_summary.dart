import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rupify/models/expense.dart';
import 'package:rupify/providers/expense_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/models/category_spending.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:google_fonts/google_fonts.dart';

class SpendingSummary extends StatelessWidget {
  const SpendingSummary({super.key});

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    // Get category-wise spending
    final categorySpending = expenseProvider.getCategorySpending();
    
    // Sort by amount (descending)
    categorySpending.sort((a, b) => b.amount.compareTo(a.amount));
    
    // Calculate total spending
    final totalSpending = categorySpending.fold(
      0.0,
      (sum, item) => sum + item.amount,
    );
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isHindi ? 'श्रेणी-वार खर्च' : 'Category-wise Spending',
              style: GoogleFonts.montserrat(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            if (categorySpending.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.pie_chart_outline,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        isHindi ? 'कोई डेटा उपलब्ध नहीं है' : 'No data available',
                        style: GoogleFonts.lato(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              )
            else
              SizedBox(
                height: 180, // Fixed height for the chart
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: PieChart(
                        PieChartData(
                          sections: _buildPieChartSections(categorySpending, totalSpending),
                          centerSpaceRadius: 30, // Reduced from default
                          sectionsSpace: 2, // Reduced space between sections
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 4,
                      child: ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: categorySpending.take(5).length,
                        itemBuilder: (context, index) {
                          final item = categorySpending[index];
                          final percentage = totalSpending > 0
                              ? (item.amount / totalSpending * 100).toStringAsFixed(1)
                              : '0.0';
                          
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4.0),
                            child: Row(
                              children: [
                                Container(
                                  width: 12,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: item.category.color,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    isHindi
                                        ? item.category.hindiName
                                        : item.category.displayName,
                                    style: GoogleFonts.lato(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                Text(
                                  '$percentage%',
                                  style: GoogleFonts.montserrat(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 16),
            Text(
              isHindi
                  ? 'कुल खर्च: ${expenseProvider.formatCurrency(totalSpending)}'
                  : 'Total Spending: ${expenseProvider.formatCurrency(totalSpending)}',
              style: GoogleFonts.montserrat(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  List<PieChartSectionData> _buildPieChartSections(
    List<CategorySpending> categories,
    double totalSpending,
  ) {
    return categories
        .take(5)
        .map(
          (item) => PieChartSectionData(
            color: item.category.color,
            value: item.amount,
            title: '',
            radius: 40, // Reduced from 50
            titleStyle: const TextStyle(
              fontSize: 10, // Reduced from 12
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        )
        .toList();
  }
}
