import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:rupify/providers/api_products_provider.dart';
import 'package:rupify/providers/expense_provider.dart';
import 'package:rupify/providers/orders_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/providers/products_provider.dart';
import 'package:rupify/providers/coins_provider.dart';
import 'package:rupify/providers/auth_provider.dart';
import 'package:rupify/providers/todo_provider.dart';
import 'package:rupify/screens/add_expense_screen.dart';
import 'package:rupify/screens/home_screen.dart';
import 'package:rupify/screens/main_screen.dart';
import 'package:rupify/screens/check_in_screen.dart';
import 'package:rupify/screens/products_screen.dart';
import 'package:rupify/screens/todo_screen.dart';
import 'package:rupify/utils/app_theme.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:rupify/utils/notification_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

// Global navigator key to use for notification navigation
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp();
  
  // Initialize notification service
  await NotificationService().init();
  
  // Set up notification tap handler
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
      
  // Check if app was opened from a notification
  final NotificationAppLaunchDetails? notificationAppLaunchDetails =
      await flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();
      
  if (notificationAppLaunchDetails?.didNotificationLaunchApp ?? false) {
    // App was launched from notification
    // We'll handle this after the app is built
  }
  
  // Add error handling
  FlutterError.onError = (details) {
    FlutterError.presentError(details);
    if (kReleaseMode) {
      // Log to crash reporting service in release mode
    }
  };
  
  // Initialize the Mobile Ads SDK
  await MobileAds.instance.initialize();
  
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ChangeNotifierProvider(create: (_) => ExpenseProvider()),
        ChangeNotifierProvider(create: (_) => CoinsProvider()),
        ChangeNotifierProvider(create: (_) => TodoProvider()),
        ChangeNotifierProvider(create: (_) => ApiProductsProvider()),
        ChangeNotifierProvider(create: (_) => OrdersProvider()),
      ],
      child: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, _) {
          return MaterialApp(
            title: 'Rupify',
            debugShowCheckedModeBanner: false,
            navigatorKey: navigatorKey, // Use the global navigator key
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: settingsProvider.themeMode,
            locale: settingsProvider.isHindi ? const Locale('hi') : const Locale('en'),
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en'),
              Locale('hi'),
            ],
            home: const HomeScreen(),
            routes: {
              '/home': (context) => const HomeScreen(),
              '/check_in': (context) => const CheckInScreen(),
              '/rewards': (context) => const ProductsScreen(),
              '/add_expense':(context) => const AddExpenseScreen(),
              '/todo':(context) => const TodoScreen(),
            },
          );
        },
      ),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    
    // Handle notification tap after app is built
    Future.delayed(Duration.zero, () {
      FlutterLocalNotificationsPlugin()
          .getNotificationAppLaunchDetails()
          .then((details) {
        if (details?.didNotificationLaunchApp ?? false) {
          final String? payload = details?.notificationResponse?.payload;
          if (payload != null && payload == '/todo') {
            navigatorKey.currentState?.pushNamed('/todo');
          }
        }
      });
    });
    
    return MaterialApp(
      title: 'Rupify',
      debugShowCheckedModeBanner: false,
      navigatorKey: navigatorKey, // Use the global navigator key
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: settingsProvider.themeMode,
      locale: settingsProvider.isHindi ? const Locale('hi') : const Locale('en'),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'),
        Locale('hi'),
      ],
      home: const HomeScreen(),
      routes: {
        '/home': (context) => const HomeScreen(),
        '/check_in': (context) => const CheckInScreen(),
        '/rewards': (context) => const ProductsScreen(),
        '/add_expense':(context) => const AddExpenseScreen(),
        '/todo':(context) => const TodoScreen(),
      },
    );
  }
}
