import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:rupify/models/order.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:rupify/providers/orders_provider.dart';

class OrderDetailsScreen extends StatefulWidget {
  final Order order;
  
  const OrderDetailsScreen({Key? key, required this.order}) : super(key: key);
  
  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _products = [];
  
  @override
  void initState() {
    super.initState();
    _fetchProductDetails();
  }
  
  Future<void> _fetchProductDetails() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final ordersProvider = Provider.of<OrdersProvider>(context, listen: false);
      final products = await ordersProvider.fetchOrderProductDetails(widget.order.id);
      
      setState(() {
        _products = products;
      });
    } catch (e) {
      print('Error fetching product details: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    // Format date
    DateTime orderDate;
    try {
      orderDate = DateTime.parse(widget.order.date);
    } catch (e) {
      orderDate = DateTime.now();
    }
    final formattedDate = '${orderDate.day}/${orderDate.month}/${orderDate.year}';
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isHindi ? 'ऑर्डर विवरण' : 'Order Details'),
      ),
      body: _isLoading 
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order summary card
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                isHindi ? 'ऑर्डर आईडी' : 'Order ID',
                                style: GoogleFonts.montserrat(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: _getStatusColor(widget.order.status ?? 'pending').withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(color: _getStatusColor(widget.order.status ?? 'pending')),
                                ),
                                child: Text(
                                  isHindi
                                      ? _getStatusInHindi(widget.order.status ?? 'pending')
                                      : (widget.order.status ?? 'pending'),
                                  style: GoogleFonts.montserrat(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: _getStatusColor(widget.order.status ?? 'pending'),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '#${widget.order.id}',
                            style: GoogleFonts.montserrat(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Divider(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                isHindi ? 'ऑर्डर तिथि' : 'Order Date',
                                style: GoogleFonts.montserrat(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              Text(
                                formattedDate,
                                style: GoogleFonts.montserrat(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Product Details
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isHindi ? 'उत्पाद विवरण' : 'Product Details',
                            style: GoogleFonts.montserrat(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          if (_products.isEmpty)
                            Text(
                              isHindi ? 'कोई उत्पाद नहीं मिला' : 'No products found',
                              style: GoogleFonts.lato(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                            )
                          else
                            ..._products.map((product) => Column(
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Product image
                                    if (product['image'] != null)
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(8),
                                        child: Image.network(
                                          product['image'],
                                          width: 80,
                                          height: 80,
                                          fit: BoxFit.cover,
                                          errorBuilder: (context, error, stackTrace) {
                                            return Container(
                                              width: 80,
                                              height: 80,
                                              color: Colors.grey.shade200,
                                              child: const Icon(Icons.image_not_supported),
                                            );
                                          },
                                        ),
                                      ),
                                    
                                    const SizedBox(width: 12),
                                    
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            product['name'] ?? 'Unknown Product',
                                            style: GoogleFonts.montserrat(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            '${isHindi ? 'मात्रा' : 'Quantity'}: ${product['quantity']}',
                                            style: GoogleFonts.lato(
                                              fontSize: 14,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            '${isHindi ? 'मूल्य' : 'Price'}: ₹${product['price']}',
                                            style: GoogleFonts.lato(
                                              fontSize: 14,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                
                                if (product['description'] != null && product['description'].isNotEmpty) ...[
                                  const Divider(height: 24),
                                  Text(
                                    isHindi ? 'विवरण' : 'Description',
                                    style: GoogleFonts.montserrat(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey.shade700,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    product['description'],
                                    style: GoogleFonts.lato(
                                      fontSize: 14,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                                
                                if (_products.indexOf(product) < _products.length - 1)
                                  const Divider(height: 24),
                              ],
                            )).toList(),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Shipping Details
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isHindi ? 'शिपिंग विवरण' : 'Shipping Details',
                            style: GoogleFonts.montserrat(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          // Customer name
                          Row(
                            children: [
                              Icon(Icons.person_outline, size: 18, color: Colors.grey.shade600),
                              const SizedBox(width: 8),
                              Text(
                                isHindi ? 'नाम' : 'Name',
                                style: GoogleFonts.montserrat(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  widget.order.customerName,
                                  style: GoogleFonts.montserrat(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.end,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          
                          // Phone
                          Row(
                            children: [
                              Icon(Icons.phone_outlined, size: 18, color: Colors.grey.shade600),
                              const SizedBox(width: 8),
                              Text(
                                isHindi ? 'फोन' : 'Phone',
                                style: GoogleFonts.montserrat(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  widget.order.phonePrimary,
                                  style: GoogleFonts.montserrat(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.end,
                                ),
                              ),
                            ],
                          ),
                          
                          if (widget.order.phoneAlternate != null && widget.order.phoneAlternate!.isNotEmpty) ...[
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Icon(Icons.phone_outlined, size: 18, color: Colors.grey.shade600),
                                const SizedBox(width: 8),
                                Text(
                                  isHindi ? 'वैकल्पिक फोन' : 'Alternate Phone',
                                  style: GoogleFonts.montserrat(
                                    fontSize: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    widget.order.phoneAlternate!,
                                    style: GoogleFonts.montserrat(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    textAlign: TextAlign.end,
                                  ),
                                ),
                              ],
                            ),
                          ],
                          
                          const SizedBox(height: 12),
                          
                          // Address
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(Icons.location_on_outlined, size: 18, color: Colors.grey.shade600),
                              const SizedBox(width: 8),
                              Text(
                                isHindi ? 'पता' : 'Address',
                                style: GoogleFonts.montserrat(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  widget.order.address,
                                  style: GoogleFonts.montserrat(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.end,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Support section
                  Center(
                    child: Column(
                      children: [
                        Text(
                          isHindi ? 'सहायता की आवश्यकता है?' : 'Need help with your order?',
                          style: GoogleFonts.montserrat(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () async {
                            // Launch WhatsApp or phone call
                            final phoneNumber = '+919999999999'; // Replace with actual support number
                            final Uri telUri = Uri.parse('tel:$phoneNumber');
                            if (await canLaunchUrl(telUri)) {
                              await launchUrl(telUri);
                            }
                          },
                          icon: const Icon(Icons.support_agent),
                          label: Text(
                            isHindi ? 'सहायता से संपर्क करें' : 'Contact Support',
                            style: GoogleFonts.montserrat(fontWeight: FontWeight.bold),
                          ),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  String _getStatusInHindi(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'पूर्ण';
      case 'processing':
        return 'प्रोसेसिंग';
      case 'pending':
        return 'लंबित';
      case 'cancelled':
        return 'रद्द';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'processing':
        return Colors.blue;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
