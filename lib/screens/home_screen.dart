import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rupify/providers/expense_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/providers/coins_provider.dart';
import 'package:rupify/screens/add_expense_screen.dart';
import 'package:rupify/screens/expense_list_screen.dart';
import 'package:rupify/screens/settings_screen.dart';
import 'package:rupify/screens/products_screen.dart';
import 'package:rupify/screens/check_in_screen.dart';
import 'package:rupify/screens/todo_screen.dart';
import 'package:rupify/widgets/budget_progress.dart';
import 'package:rupify/widgets/recent_expenses.dart';
import 'package:rupify/widgets/spending_summary.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  int _selectedIndex = 0;
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadBannerAd();
  }
  
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _bannerAd?.dispose();
    super.dispose();
  }
  
  void _loadBannerAd() {
    _bannerAd = BannerAd(
      adUnitId: 'ca-app-pub-8904820275824881/5886169300', // Test ad unit ID
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (_) {
          setState(() {
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
        },
      ),
    );

    _bannerAd?.load();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // This helps with state restoration when the app is resumed
    if (state == AppLifecycleState.resumed) {
      // Refresh data if needed
      Provider.of<ExpenseProvider>(context, listen: false).refreshData();
    }
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final coinsProvider = Provider.of<CoinsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    final List<Widget> screens = [
      _buildHomeTab(),
      const ExpenseListScreen(),
      const ProductsScreen(),
      const TodoScreen(),
      const SettingsScreen(),
    ];
    
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        appBar: _selectedIndex == 0
            ? AppBar(
                backgroundColor: const Color(0xFF36916D).withOpacity(0.1),
                elevation: 0,
                title: Align(
                  alignment: Alignment.centerLeft,
                  child: Image.asset(
                    'assets/images/logo.png',
                    height: 40,
                    width: 120, // Adjusted width for proper display
                    fit: BoxFit.contain,
                  ),
                ),
                actions: [
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                    padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4.0),
                    decoration: BoxDecoration(
                      color: Colors.amber.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: Colors.amber, width: 1),
                    ),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => const CheckInScreen(),
                          ),
                        );
                      },
                      child: Row(
                        children: [
                          const Icon(Icons.monetization_on, color: Colors.amber, size: 20),
                          const SizedBox(width: 4),
                          Text(
                            '${coinsProvider.coins}',
                            style: GoogleFonts.montserrat(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.notifications_outlined),
                    onPressed: () {
                      // Show notifications or reminders
                    },
                  ),
                ],
              )
            : null,
        body: screens[_selectedIndex],
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            child: BottomNavigationBar(
              currentIndex: _selectedIndex,
              onTap: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              items: [
                BottomNavigationBarItem(
                  icon: const Icon(Icons.home_outlined),
                  activeIcon: const Icon(Icons.home),
                  label: isHindi ? 'होम' : 'Home',
                ),
                BottomNavigationBarItem(
                  icon: const Icon(Icons.list_alt_outlined),
                  activeIcon: const Icon(Icons.list_alt),
                  label: isHindi ? 'खर्च' : 'Expenses',
                ),
                BottomNavigationBarItem(
                  icon: const Icon(Icons.shopping_cart_outlined),
                  activeIcon: const Icon(Icons.shopping_cart),
                  label: isHindi ? 'रिवॉर्ड्स' : 'Rewards',
                ),
                BottomNavigationBarItem(
                  icon: const Icon(Icons.check_circle_outline),
                  activeIcon: const Icon(Icons.check_circle),
                  label: isHindi ? 'टूडू' : 'Todo',
                ),
                BottomNavigationBarItem(
                  icon: const Icon(Icons.settings_outlined),
                  activeIcon: const Icon(Icons.settings),
                  label: isHindi ? 'सेटिंग्स' : 'Settings',
                ),
              ],
              type: BottomNavigationBarType.fixed,
            ),
          ),
        ),
        floatingActionButton: _selectedIndex == 0 || _selectedIndex == 1 
            ? FloatingActionButton(
                onPressed: () {
                  if (_selectedIndex == 0 || _selectedIndex == 1) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => const AddExpenseScreen(),
                      ),
                    );
                  } else if (_selectedIndex == 3) {
                    // Show add todo dialog when on Todo screen
                    _showAddTodoDialog(context);
                  }
                },
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                elevation: 4,
                child: const Icon(Icons.add),
              )
            : null,
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        restorationId: 'homeScreen',
      ),
    );
  }

  Future<bool> _onWillPop() async {
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isHindi ? 'ऐप से बाहर निकलें?' : 'Exit App?'),
        content: Text(
          isHindi 
              ? 'क्या आप वाकई ऐप से बाहर निकलना चाहते हैं?'
              : 'Do you really want to exit the app?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(isHindi ? 'रद्द करें' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(isHindi ? 'बाहर निकलें' : 'Exit'),
          ),
        ],
      ),
    ) ?? false;
  }

  void _showAddTodoDialog(BuildContext context) {
    final TextEditingController titleController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isHindi ? 'नया टूडू जोड़ें' : 'Add New Todo'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: InputDecoration(
                  labelText: isHindi ? 'शीर्षक' : 'Title',
                  hintText: isHindi ? 'टूडू का शीर्षक' : 'Todo title',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: InputDecoration(
                  labelText: isHindi ? 'विवरण' : 'Description',
                  hintText: isHindi ? 'टूडू का विवरण' : 'Todo description',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isHindi ? 'रद्द करें' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (titleController.text.isNotEmpty) {
                // Always save directly to SharedPreferences
                final prefs = await SharedPreferences.getInstance();
                final todosJson = prefs.getStringList('todos') ?? [];
                final todos = todosJson
                    .map((json) => TodoItem.fromJson(jsonDecode(json)))
                    .toList();
                
                todos.add(TodoItem(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  title: titleController.text,
                  description: descriptionController.text,
                  date: DateTime.now(),
                  isCompleted: false,
                ));
                
                final updatedTodosJson = todos
                    .map((todo) => jsonEncode(todo.toJson()))
                    .toList();
                setState(() {
                   
                });
                await prefs.setStringList('todos', updatedTodosJson);
                
                Navigator.pop(context);
                // Show confirmation
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(isHindi ? 'टूडू जोड़ा गया' : 'Todo added-'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF36916D),
              foregroundColor: Colors.white,
            ),
            child: Text(isHindi ? 'जोड़ें' : 'Add'),
          ),
        ],
      ),
    );
  }

  Widget _buildHomeTab() {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Monthly summary card
                Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isHindi ? 'मासिक सारांश' : 'Monthly Summary',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  isHindi ? 'खर्च किया' : 'Spent',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                                Text(
                                  expenseProvider.formatCurrency(
                                      expenseProvider.totalSpentThisMonth),
                                  style: Theme.of(context).textTheme.titleLarge!.copyWith(
                                        color: Colors.red,
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  isHindi ? 'शेष बजट' : 'Remaining',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                                Text(
                                  expenseProvider.formatCurrency(
                                      expenseProvider.remainingBudget),
                                  style: Theme.of(context).textTheme.titleLarge!.copyWith(
                                        color: Colors.green,
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        BudgetProgress(
                          spent: expenseProvider.totalSpentThisMonth,
                          budget: expenseProvider.monthlyBudget,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Recent expenses
                Text(
                  isHindi ? 'हाल के खर्च' : 'Recent Expenses',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                const RecentExpenses(),
                
                // Add Daily Check-in Card here
                const SizedBox(height: 24),
                Card(
                  elevation: 3,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: Colors.amber.withOpacity(0.5),
                      width: 1,
                    ),
                  ),
                  child: InkWell(
                    onTap: () {
                      // Navigate to Check-in Screen
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const CheckInScreen(),
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.amber.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.calendar_today,
                              color: Colors.amber,
                              size: 28,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  isHindi ? 'दैनिक चेक-इन' : 'Daily Check-in',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  isHindi
                                      ? 'रोज़ चेक-इन करें और कॉइन्स कमाएं'
                                      : 'Check in daily to earn coins',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Icon(Icons.arrow_forward_ios, size: 16),
                        ],
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Spending summary
                Text(
                  isHindi ? 'खर्च सारांश' : 'Spending Summary',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                const SpendingSummary(),
                
                const SizedBox(height: 24),
                
                // Quick actions
                Text(
                  isHindi ? 'त्वरित कार्य' : 'Quick Actions',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      context,
                      isHindi ? 'खर्च जोड़ें' : 'Add Expense',
                      Icons.add_circle,
                      Colors.green,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => const AddExpenseScreen()),
                      ),
                    ),
                    _buildActionButton(
                      context,
                      isHindi ? 'टूडू जोड़ें' : 'Add Todo',
                      Icons.check_circle,
                      Colors.orange,
                      () {
                        setState(() => _selectedIndex = 3);
                        _showAddTodoDialog(context);
                      },
                    ),
                    _buildActionButton(
                      context,
                      isHindi ? 'रिवॉर्ड्स' : 'Rewards',
                      Icons.shopping_cart,
                      Colors.purple,
                      () => setState(() => _selectedIndex = 2),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        
        // Banner Ad
        if (_isAdLoaded)
          SizedBox(
            height: _bannerAd!.size.height.toDouble(),
            width: _bannerAd!.size.width.toDouble(),
            child: AdWidget(ad: _bannerAd!),
          ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return InkWell(
      onTap: onPressed,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 36,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }
}

class TodoItem {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final bool isCompleted;

  TodoItem({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.isCompleted,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'date': date.millisecondsSinceEpoch,
      'isCompleted': isCompleted,
    };
  }
  
  factory TodoItem.fromJson(Map<String, dynamic> json) {
    return TodoItem(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      date: DateTime.fromMillisecondsSinceEpoch(json['date']),
      isCompleted: json['isCompleted'],
    );
  }
}
