import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:rupify/models/product.dart';
import 'package:rupify/providers/auth_provider.dart';
import 'package:rupify/providers/coins_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/screens/auth_screen.dart';
import 'package:rupify/screens/order_confirmation_screen.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class ProductDetailScreen extends StatefulWidget {
  final Product product;

  const ProductDetailScreen({
    super.key,
    required this.product,
  });

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen> {
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  void _loadBannerAd() {
    _bannerAd = BannerAd(
      adUnitId: 'ca-app-pub-8904820275824881/**********', // Test ad unit ID
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (_) {
          setState(() {
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
        },
      ),
    );

    _bannerAd?.load();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final coinsProvider = Provider.of<CoinsProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isHindi = settingsProvider.isHindi;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final canAfford = coinsProvider.coins >= widget.product.coinPrice;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.product.name),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                const Icon(Icons.monetization_on, color: Colors.amber),
                const SizedBox(width: 4),
                Text(
                  '${coinsProvider.coins}',
                  style: GoogleFonts.montserrat(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Image with better styling
                  Hero(
                    tag: 'product-${widget.product.id}',
                    child: AspectRatio(
                      aspectRatio: 16 / 9,
                      child: Container(
                        decoration: BoxDecoration(
                          color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Image.asset(
                          widget.product.imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                              child: const Icon(
                                Icons.image_not_supported,
                                size: 80,
                                color: Colors.grey,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                  
                  // Product Details with improved styling
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Product Name
                        Text(
                          widget.product.name,
                          style: GoogleFonts.montserrat(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Price with better styling
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                          decoration: BoxDecoration(
                            color: isDarkMode 
                                ? canAfford ? Colors.green.shade900.withOpacity(0.3) : Colors.red.shade900.withOpacity(0.3)
                                : canAfford ? Colors.green.shade50 : Colors.red.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isDarkMode
                                  ? canAfford ? Colors.green.shade700.withOpacity(0.5) : Colors.red.shade700.withOpacity(0.5)
                                  : canAfford ? Colors.green.shade200 : Colors.red.shade200,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.monetization_on,
                                color: Colors.amber,
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${widget.product.coinPrice}',
                                style: GoogleFonts.montserrat(
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color: isDarkMode
                                      ? canAfford ? Colors.green.shade300 : Colors.red.shade300
                                      : canAfford ? Colors.green.shade700 : Colors.red.shade700,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                isHindi ? 'सिक्के' : 'coins',
                                style: GoogleFonts.montserrat(
                                  fontSize: 16,
                                  color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 8),

                        // Not enough coins message
                        if (!canAfford)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Row(
                              children: [
                                const Icon(Icons.warning_amber_rounded, color: Colors.red, size: 16),
                                const SizedBox(width: 8),
                                Text(
                                  isHindi ? 'आपके पास पर्याप्त सिक्के नहीं हैं!' : 'You don\'t have enough coins!',
                                  style: GoogleFonts.montserrat(
                                    fontSize: 14,
                                    color: Colors.red,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),

                        const SizedBox(height: 24),

                        // Description with better styling
                        Text(
                          isHindi ? 'विवरण' : 'Description f',
                          style: GoogleFonts.montserrat(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isDarkMode ? Colors.grey.shade900 : Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                            ),
                          ),
                          child: Text(
                            widget.product.description,
                            style: GoogleFonts.montserrat(
                              fontSize: 15,
                              height: 1.5,
                              color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Features with better styling
                        if (widget.product.features.isNotEmpty) ...[
                          Text(
                            isHindi ? 'विशेषताएं' : 'Features',
                            style: GoogleFonts.montserrat(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: isDarkMode ? Colors.grey.shade900 : Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: widget.product.features.map((feature) => Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Icon(
                                      Icons.check_circle,
                                      color: Theme.of(context).primaryColor,
                                      size: 18,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        feature,
                                        style: GoogleFonts.montserrat(
                                          fontSize: 14,
                                          height: 1.4,
                                          color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )).toList(),
                            ),
                          ),
                        ],

                        const SizedBox(height: 32),

                        // Redemption Instructions with better styling
                        Text(
                          isHindi ? 'रिडेम्पशन निर्देश' : 'Redemption Instructions',
                          style: GoogleFonts.montserrat(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isDarkMode ? Colors.grey.shade900 : Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                isHindi
                                    ? '1. अपने ऑर्डर को पूरा करें'
                                    : '1. Complete your order',
                                style: GoogleFonts.lato(
                                  fontSize: 14,
                                  height: 1.5,
                                  color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                isHindi
                                    ? '2. आपको ईमेल या एसएमएस के माध्यम से रिडेम्पशन कोड प्राप्त होगा'
                                    : '2. You will receive a redemption code via email or SMS',
                                style: GoogleFonts.lato(
                                  fontSize: 14,
                                  height: 1.5,
                                  color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                isHindi
                                    ? '3. कोड का उपयोग करके अपना रिवॉर्ड प्राप्त करें'
                                    : '3. Use the code to claim your reward',
                                style: GoogleFonts.lato(
                                  fontSize: 14,
                                  height: 1.5,
                                  color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Description
                        Text(
                          isHindi ? 'विवरण' : 'Description',
                          style: GoogleFonts.montserrat(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          widget.product.description,
                          style: GoogleFonts.lato(
                            fontSize: 16,
                            height: 1.5,
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // User Profile Section (if logged in)
                        if (authProvider.isLoggedIn) ...[
                          Text(
                            isHindi ? 'डिलीवरी विवरण' : 'Delivery Details',
                            style: GoogleFonts.montserrat(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Card(
                            elevation: 2,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildProfileDetail(
                                    context,
                                    isHindi ? 'नाम' : 'Name',
                                    authProvider.currentUser?.name ?? '',
                                    Icons.person,
                                  ),
                                  const Divider(height: 24),
                                  _buildProfileDetail(
                                    context,
                                    isHindi ? 'ईमेल' : 'Email',
                                    authProvider.currentUser?.email ?? '',
                                    Icons.email,
                                  ),
                                  const Divider(height: 24),
                                  _buildProfileDetail(
                                    context,
                                    isHindi ? 'फोन' : 'Phone',
                                    authProvider.currentUser?.phone ?? 
                                      (isHindi ? 'अपडेट करें' : 'Update'),
                                    Icons.phone,
                                    isPlaceholder: authProvider.currentUser?.phone == null,
                                  ),
                                  const Divider(height: 24),
                                  _buildProfileDetail(
                                    context,
                                    isHindi ? 'पता' : 'Address',
                                    authProvider.currentUser?.address ?? 
                                      (isHindi ? 'अपडेट करें' : 'Update'),
                                    Icons.location_on,
                                    isPlaceholder: authProvider.currentUser?.address == null,
                                  ),
                                  const SizedBox(height: 16),
                                  SizedBox(
                                    width: double.infinity,
                                    child: ElevatedButton(
                                      onPressed: () => _showUpdateProfileDialog(context),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue,
                                        foregroundColor: Colors.white,
                                      ),
                                      child: Text(
                                        isHindi ? 'प्रोफ़ाइल अपडेट करें' : 'Update Profile',
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Purchase Button - Fixed at bottom
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey.shade900 : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
              border: Border(
                top: BorderSide(
                  color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                ),
              ),
            ),
            child: ElevatedButton(
              onPressed: canAfford ? () => _showPurchaseConfirmation(context) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                disabledBackgroundColor: Colors.grey.shade400,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: Text(
                canAfford
                    ? (isHindi ? 'खरीदें' : 'Purchase')
                    : (isHindi ? 'पर्याप्त सिक्के नहीं' : 'Not Enough Coins'),
                style: GoogleFonts.montserrat(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          // Banner Ad
          if (_isAdLoaded)
            SizedBox(
              height: _bannerAd!.size.height.toDouble(),
              width: _bannerAd!.size.width.toDouble(),
              child: AdWidget(ad: _bannerAd!),
            ),
        ],
      ),
    );
  }

  Widget _buildProfileDetail(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    bool isPlaceholder = false,
  }) {
    return Row(
      children: [
        Icon(icon, color: Colors.grey.shade600),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isPlaceholder ? Colors.grey : Colors.black,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showUpdateProfileDialog(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    
    final phoneController = TextEditingController(text: authProvider.currentUser?.phone);
    final addressController = TextEditingController(text: authProvider.currentUser?.address);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isHindi ? 'प्रोफ़ाइल अपडेट करें' : 'Update Profile'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: phoneController,
                decoration: InputDecoration(
                  labelText: isHindi ? 'फोन नंबर' : 'Phone Number',
                  prefixIcon: const Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  labelText: isHindi ? 'पता' : 'Address',
                  prefixIcon: const Icon(Icons.location_on),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isHindi ? 'रद्द करें' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              authProvider.updateUserProfile(
                phone: phoneController.text.trim(),
                address: addressController.text.trim(),
              );
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    isHindi ? 'प्रोफ़ाइल अपडेट की गई' : 'Profile updated',
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text(isHindi ? 'अपडेट करें' : 'Update'),
          ),
        ],
      ),
    );
  }

  void _handlePurchase(BuildContext context) {
    final coinsProvider = Provider.of<CoinsProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    
    // Check if delivery details are complete
    final user = authProvider.currentUser;
    if (user?.phone == null || user?.address == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isHindi
                ? 'कृपया अपना फोन नंबर और पता अपडेट करें'
                : 'Please update your phone number and address',
          ),
          backgroundColor: Colors.orange,
          action: SnackBarAction(
            label: isHindi ? 'अपडेट करें' : 'Update',
            onPressed: () => _showUpdateProfileDialog(context),
          ),
        ),
      );
      return;
    }
    
    // Confirm purchase
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isHindi ? 'खरीदारी की पुष्टि करें' : 'Confirm Purchase'),
        content: Text(
          isHindi
              ? 'क्या आप वाकई ${widget.product.coinPrice} सिक्कों के लिए ${widget.product.name} खरीदना चाहते हैं?'
              : 'Are you sure you want to purchase ${widget.product.name} for ${widget.product.coinPrice} coins?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isHindi ? 'रद्द करें' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              
              // Process purchase
              final success = await coinsProvider.spendCoins(widget.product.coinPrice);
              
              if (success) {
                // Navigate to confirmation screen
                if (context.mounted) {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (_) => OrderConfirmationScreen(product: widget.product),
                    ),
                  );
                }
              } else {
                // Show error (should not happen as button is disabled if not enough coins)
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        isHindi
                            ? 'पर्याप्त सिक्के नहीं हैं'
                            : 'Not enough coins',
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: Text(isHindi ? 'खरीदें' : 'Purchase'),
          ),
        ],
      ),
    );
  }

  Widget _buildRedemptionStep(String number, String title, String description, bool isDarkMode) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 28,
          height: 28,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              number,
              style: GoogleFonts.montserrat(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.montserrat(
                  fontWeight: FontWeight.w600,
                  fontSize: 15,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: GoogleFonts.montserrat(
                  fontSize: 14,
                  height: 1.4,
                  color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade700,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showPurchaseConfirmation(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final coinsProvider = Provider.of<CoinsProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Check if user is logged in
    if (!authProvider.isLoggedIn) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(isHindi ? 'लॉगिन आवश्यक' : 'Login Required'),
          content: Text(
            isHindi
                ? 'खरीदारी करने के लिए कृपया लॉगिन करें'
                : 'Please login to make a purchase',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(isHindi ? 'रद्द करें' : 'Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => const AuthScreen(fromOrders: false),
                  ),
                );
              },
              child: Text(isHindi ? 'लॉगिन करें' : 'Login'),
            ),
          ],
        ),
      );
      return;
    }
    
    // Check if delivery details are complete
    final user = authProvider.currentUser;
    if (user?.phone == null || user?.address == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isHindi
                ? 'कृपया अपना फोन नंबर और पता अपडेट करें'
                : 'Please update your phone number and address',
          ),
          backgroundColor: Colors.orange,
          action: SnackBarAction(
            label: isHindi ? 'अपडेट करें' : 'Update',
            onPressed: () => _showUpdateProfileDialog(context),
          ),
        ),
      );
      return;
    }
    
    // Show bottom sheet for purchase confirmation
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: isDarkMode ? Colors.grey.shade900 : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 5,
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              isHindi ? 'खरीदारी की पुष्टि करें' : 'Confirm Purchase',
              style: GoogleFonts.montserrat(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                ),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.asset(
                          widget.product.imageUrl,
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 60,
                              height: 60,
                              color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
                              child: const Icon(Icons.image_not_supported, color: Colors.grey),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.product.name,
                              style: GoogleFonts.montserrat(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                color: isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                const Icon(Icons.monetization_on, color: Colors.amber, size: 16),
                                const SizedBox(width: 4),
                                Text(
                                  '${widget.product.coinPrice} ${isHindi ? 'सिक्के' : 'coins'}',
                                  style: GoogleFonts.montserrat(
                                    color: isDarkMode ? Colors.amber.shade300 : Colors.amber.shade800,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const Divider(height: 32),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        isHindi ? 'वर्तमान सिक्के:' : 'Current Coins:',
                        style: GoogleFonts.montserrat(
                          fontSize: 14,
                          color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade700,
                        ),
                      ),
                      Text(
                        '${coinsProvider.coins}',
                        style: GoogleFonts.montserrat(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        isHindi ? 'खर्च किए जाने वाले सिक्के:' : 'Coins to Spend:',
                        style: GoogleFonts.montserrat(
                          fontSize: 14,
                          color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade700,
                        ),
                      ),
                      Text(
                        '-${widget.product.coinPrice}',
                        style: GoogleFonts.montserrat(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  const Divider(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        isHindi ? 'शेष सिक्के:' : 'Remaining Coins:',
                        style: GoogleFonts.montserrat(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
                        ),
                      ),
                      Text(
                        '${coinsProvider.coins - widget.product.coinPrice}',
                        style: GoogleFonts.montserrat(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(
                        color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                      ),
                    ),
                    child: Text(
                      isHindi ? 'रद्द करें' : 'Cancel',
                      style: GoogleFonts.montserrat(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(context);
                      
                      // Process purchase
                      final success = await coinsProvider.spendCoins(widget.product.coinPrice);
                      
                      if (success) {
                        // Navigate to confirmation screen
                        if (context.mounted) {
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                              builder: (_) => OrderConfirmationScreen(product: widget.product),
                            ),
                          );
                        }
                      } else {
                        // Show error (should not happen as button is disabled if not enough coins)
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                isHindi
                                    ? 'पर्याप्त सिक्के नहीं हैं'
                                    : 'Not enough coins',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      elevation: 0,
                    ),
                    child: Text(
                      isHindi ? 'खरीदें' : 'Purchase',
                      style: GoogleFonts.montserrat(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
