import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:rupify/models/product_api.dart';
import 'package:rupify/providers/api_products_provider.dart';
import 'package:rupify/providers/coins_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/screens/place_order_screen.dart';

class ProductApiDetailScreen extends StatefulWidget {
  final String productId;

  const ProductApiDetailScreen({
    super.key,
    required this.productId,
  });

  @override
  State<ProductApiDetailScreen> createState() => _ProductApiDetailScreenState();
}

class _ProductApiDetailScreenState extends State<ProductApiDetailScreen> {
  int _currentImageIndex = 0;
  
  // Remove this line completely
  PreferredSizeWidget? get bottomNavigationBar => null;

  @override
  Widget build(BuildContext context) {
    final productsProvider = Provider.of<ApiProductsProvider>(context);
    final coinsProvider = Provider.of<CoinsProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    final product = productsProvider.getProductById(widget.productId);
    
    if (product == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            isHindi ? 'उत्पाद विवरण' : 'Product Details',
            style: GoogleFonts.montserrat(fontWeight: FontWeight.bold),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 60, color: Colors.red.shade300),
              const SizedBox(height: 16),
              Text(
                isHindi ? 'उत्पाद नहीं मिला' : 'Product not found',
                style: GoogleFonts.montserrat(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back),
                label: Text(isHindi ? 'वापस जाएं' : 'Go Back'),
              ),
            ],
          ),
        ),
      );
    }

    final canAfford = coinsProvider.coins >= product.coinPrice;
    final allImages = [product.image, ...product.additionalImages];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isHindi ? 'उत्पाद विवरण' : 'Product Details',
          style: GoogleFonts.montserrat(fontWeight: FontWeight.bold),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Row(
              children: [
                const Icon(Icons.monetization_on, color: Colors.amber),
                const SizedBox(width: 4),
                Text(
                  '${coinsProvider.coins}',
                  style: GoogleFonts.montserrat(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Images with improved carousel
                  Hero(
                    tag: 'product-${product.id}',
                    child: SizedBox(
                      height: 300,
                      child: Stack(
                        children: [
                          PageView.builder(
                            itemCount: allImages.length,
                            onPageChanged: (index) {
                              setState(() {
                                _currentImageIndex = index;
                              });
                            },
                            itemBuilder: (context, index) {
                              return Image.network(
                                allImages[index],
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) => Container(
                                  color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                                  child: const Center(
                                    child: Icon(
                                      Icons.image_not_supported,
                                      size: 50,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                          
                          // Improved image indicators
                          if (allImages.length > 1)
                            Positioned(
                              bottom: 16,
                              left: 0,
                              right: 0,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: List.generate(
                                  allImages.length,
                                  (index) => AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    margin: const EdgeInsets.symmetric(horizontal: 4),
                                    width: _currentImageIndex == index ? 16 : 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(4),
                                      color: _currentImageIndex == index
                                          ? Theme.of(context).primaryColor
                                          : Colors.white.withOpacity(0.5),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          
                          // Out of stock overlay with better styling
                          if (!product.inStock)
                            Container(
                              color: Colors.black.withOpacity(0.6),
                              child: Center(
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                                  decoration: BoxDecoration(
                                    color: Colors.red.withOpacity(0.8),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    isHindi ? 'स्टॉक में नहीं है' : 'Out of Stock',
                                    style: GoogleFonts.montserrat(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                  
                  // Product Details with improved styling
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Product Name
                        Text(
                          product.name,
                          style: GoogleFonts.montserrat(
                            fontWeight: FontWeight.w500, // Changed from bold to w500
                            fontSize: 16, // Adjust size as needed for detail screen
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 12),
                        
                        // Price and Coin Price with better layout
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: isDarkMode ? Colors.green.shade900 : Colors.green.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: isDarkMode ? Colors.green.shade700 : Colors.green.shade200,
                                ),
                              ),
                              child: Text(
                                '₹${product.price}',
                                style: GoogleFonts.montserrat(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: isDarkMode ? Colors.green.shade300 : Colors.green.shade700,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: isDarkMode ? Colors.amber.shade900.withOpacity(0.3) : Colors.amber.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: isDarkMode ? Colors.amber.shade700.withOpacity(0.5) : Colors.amber.shade200,
                                ),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.monetization_on, color: Colors.amber, size: 18),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${product.coinPrice}',
                                    style: GoogleFonts.montserrat(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: canAfford 
                                        ? (isDarkMode ? Colors.amber.shade300 : Colors.amber.shade800)
                                        : Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        
                        if (!canAfford)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Row(
                              children: [
                                const Icon(Icons.warning_amber_rounded, color: Colors.red, size: 16),
                                const SizedBox(width: 4),
                                Text(
                                  isHindi ? 'आपके पास पर्याप्त सिक्के नहीं हैं!' : 'You don\'t have enough coins!',
                                  style: GoogleFonts.montserrat(
                                    fontSize: 14,
                                    color: Colors.red,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        
                        const SizedBox(height: 16),
                        
                        // Category with better styling
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: isDarkMode ? Colors.blue.shade900.withOpacity(0.3) : Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: isDarkMode ? Colors.blue.shade700.withOpacity(0.5) : Colors.blue.shade200,
                            ),
                          ),
                          child: Text(
                            product.category,
                            style: GoogleFonts.montserrat(
                              fontSize: 14,
                              color: isDarkMode ? Colors.blue.shade300 : Colors.blue.shade800,
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Description section with better styling
                        _buildSectionHeader(isHindi ? 'विवरण' : 'Description', Icons.description, isDarkMode),
                        const SizedBox(height: 8),

                        // Using flutter_html to render HTML content with improved styling
                        if (product.description.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: isDarkMode ? Colors.grey.shade900 : Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                              ),
                            ),
                            child: Html(
                              data: product.description,
                              style: {
                                "body": Style(
                                  fontSize: FontSize(15.0),
                                  fontFamily: 'Montserrat',
                                  margin: Margins.zero,
                                  padding: HtmlPaddings.zero,
                                  color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
                                ),
                                "p": Style(
                                  margin: Margins.only(bottom: 16),
                                ),
                                "li": Style(
                                  margin: Margins.only(bottom: 8),
                                ),
                                "h1, h2, h3, h4, h5, h6": Style(
                                  color: isDarkMode ? Colors.white : Colors.black87,
                                  fontWeight: FontWeight.bold,
                                ),
                              },
                            ),
                          )
                        else
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: isDarkMode ? Colors.grey.shade900 : Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                isHindi ? 'कोई विवरण उपलब्ध नहीं है' : 'No description available',
                                style: GoogleFonts.montserrat(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          ),

                        const SizedBox(height: 24),

                        // Specifications with improved styling
                        if (product.specifications.isNotEmpty) ...[
                          _buildSectionHeader(isHindi ? 'विशेषताएं' : 'Specifications', Icons.list_alt, isDarkMode),
                          const SizedBox(height: 12),
                          
                          // Display specifications in a card
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: isDarkMode ? Colors.grey.shade900 : Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                              ),
                            ),
                            child: Column(
                              children: product.specifications.entries.map((spec) => Padding(
                                padding: const EdgeInsets.only(bottom: 12.0),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 120,
                                      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                                      decoration: BoxDecoration(
                                        color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        spec.key,
                                        style: GoogleFonts.montserrat(
                                          fontSize: 14,
                                          color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        spec.value,
                                        style: GoogleFonts.montserrat(
                                          fontSize: 14,
                                          color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )).toList(),
                            ),
                          ),
                          
                          const SizedBox(height: 16),
                        ],

                        // Add delivery information section
                        _buildSectionHeader(isHindi ? 'डिलीवरी जानकारी' : 'Delivery Information', Icons.local_shipping, isDarkMode),
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isDarkMode ? Colors.grey.shade900 : Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                            ),
                          ),
                          child: Column(
                            children: [
                              _buildInfoRow(
                                Icons.access_time,
                                isHindi ? 'प्रोसेसिंग समय' : 'Processing Time',
                                isHindi ? '1-2 कार्य दिवस' : '1-2 business days',
                                isDarkMode,
                              ),
                              const Divider(height: 24),
                              _buildInfoRow(
                                Icons.local_shipping_outlined,
                                isHindi ? 'डिलीवरी' : 'Delivery',
                                isHindi ? '3-5 कार्य दिवस' : '3-5 business days',
                                isDarkMode,
                              ),
                              const Divider(height: 24),
                              _buildInfoRow(
                                Icons.verified_outlined,
                                isHindi ? 'गारंटी' : 'Warranty',
                                product.warranty ?? (isHindi ? 'कोई गारंटी नहीं' : 'No warranty'),
                                isDarkMode,
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
     Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey.shade900 : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: SafeArea(
              child: Row(
                children: [
                  // Coin balance display
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.account_balance_wallet, color: Colors.amber),
                        const SizedBox(width: 4),
                        Text(
                          '${coinsProvider.coins}',
                          style: GoogleFonts.montserrat(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Buy button
                  Expanded(
                    child: ElevatedButton(
                      onPressed: canAfford && product.inStock
                          ? () => _handleBuyNow(context, product)
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        disabledBackgroundColor: Colors.grey.shade400,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            !product.inStock
                                ? Icons.remove_shopping_cart
                                : !canAfford
                                    ? Icons.money_off
                                    : Icons.shopping_cart_checkout,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            !product.inStock
                                ? (isHindi ? 'स्टॉक में नहीं है' : 'Out of Stock')
                                : !canAfford
                                    ? (isHindi ? 'पर्याप्त सिक्के नहीं हैं' : 'Not Enough Coins')
                                    : (isHindi ? 'अभी खरीदें' : 'Buy Now'),
                            style: GoogleFonts.montserrat(
                              fontSize: 14, // Reduced from 16
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleBuyNow(BuildContext context, ProductApi product) {
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey.shade900 : Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 5,
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              isHindi ? 'ऑर्डर की पुष्टि करें' : 'Confirm Order',
              style: GoogleFonts.montserrat(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  product.image,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    width: 60,
                    height: 60,
                    color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                    child: const Icon(Icons.image_not_supported, color: Colors.grey),
                  ),
                ),
              ),
              title: Text(
                product.name,
                style: GoogleFonts.montserrat(
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              subtitle: Row(
                children: [
                  const Icon(Icons.monetization_on, color: Colors.amber, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    '${product.coinPrice} ${isHindi ? 'सिक्के' : 'coins'}',
                    style: GoogleFonts.montserrat(
                      color: isDarkMode ? Colors.amber.shade300 : Colors.amber.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(
                        color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      isHindi ? 'रद्द करें' : 'Cancel',
                      style: GoogleFonts.montserrat(
                        fontWeight: FontWeight.w600,
                        color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => PlaceOrderScreen(product: product),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      isHindi ? 'आगे बढ़ें' : 'Proceed',
                      style: GoogleFonts.montserrat(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, bool isDarkMode) {
    return Row(
      children: [
        Icon(
          icon,
          size: 22,
          color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.montserrat(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, bool isDarkMode) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: isDarkMode ? Theme.of(context).primaryColor.withOpacity(0.8) : Theme.of(context).primaryColor,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.montserrat(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: GoogleFonts.montserrat(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
