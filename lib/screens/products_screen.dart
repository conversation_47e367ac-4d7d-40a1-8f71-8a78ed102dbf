import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:rupify/providers/api_products_provider.dart';
import 'package:rupify/providers/coins_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/providers/auth_provider.dart';
import 'package:rupify/screens/orders_screen.dart';
import 'package:rupify/screens/product_api_detail_screen.dart';
import 'package:rupify/screens/auth_screen.dart';
import 'package:rupify/models/product_api.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class ProductsScreen extends StatefulWidget {
  const ProductsScreen({super.key});

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'all';
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
    
    // Add listener to search controller
    _searchController.addListener(() {
      // This ensures the clear button appears/disappears as needed
      setState(() {});
    });
    
    // Initial data fetch
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ApiProductsProvider>(context, listen: false).fetchProducts();
    });
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadBannerAd() {
    _bannerAd = BannerAd(
      adUnitId: 'ca-app-pub-8904820275824881/**********', // Test ad unit ID
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (_) {
          setState(() {
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
        },
      ),
    );

    _bannerAd?.load();
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final apiProductsProvider = Provider.of<ApiProductsProvider>(context);
    final coinsProvider = Provider.of<CoinsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    // Determine grid columns based on screen width
    final gridColumns = screenWidth < 600 ? 2 : 3;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          isHindi ? 'रिवॉर्ड्स' : 'Rewards',
          style: GoogleFonts.montserrat(fontWeight: FontWeight.bold),
        ),
        actions: [
          // My Orders button
          IconButton(
            icon: const Icon(Icons.shopping_bag_outlined),
            onPressed: () => _navigateToOrders(context),
            tooltip: isHindi ? 'मेरे ऑर्डर' : 'My Orders',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterOptions(context),
          ),
        ],
      ),
      body: Column(
        children: [
          // Coins display at top
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.amber.withOpacity(0.2) : Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.amber),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.monetization_on, color: Colors.amber, size: 24),
                  const SizedBox(width: 8),
                  Text(
                    '${coinsProvider.coins}',
                    style: GoogleFonts.montserrat(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.amber : Colors.amber.shade800,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    isHindi ? 'सिक्के' : 'coins',
                    style: GoogleFonts.montserrat(
                      fontSize: 14,
                      color: isDarkMode ? Colors.amber.shade200 : Colors.amber.shade800,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Search bar - more compact for small screens
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                if (value.isEmpty) {
                  Provider.of<ApiProductsProvider>(context, listen: false)
                      .setSearchQuery('');
                }
              },
              onSubmitted: (value) {
                if (value.length >= 2) {
                  Provider.of<ApiProductsProvider>(context, listen: false)
                      .setSearchQuery(value);
                }
              },
              decoration: InputDecoration(
                hintText: isHindi ? 'रिवॉर्ड्स खोजें...' : 'Search rewards...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          Provider.of<ApiProductsProvider>(context, listen: false)
                              .setSearchQuery('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12),
                filled: true,
              ),
            ),
          ),
          
          // Category chips - horizontal scrollable
          SizedBox(
            height: 40,
            child: FutureBuilder<List<Map<String, String>>>(
              future: _fetchCategories(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: SizedBox(
                    height: 20, 
                    width: 20, 
                    child: CircularProgressIndicator(strokeWidth: 2)
                  ));
                }
                
                final categories = [{'slug': 'All', 'name': isHindi ? 'सभी' : 'All'}];
                if (snapshot.hasData) {
                  categories.addAll(snapshot.data!);
                }
                
                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  scrollDirection: Axis.horizontal,
                  itemCount: categories.length,
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    final isSelected = apiProductsProvider.currentCategory == category['slug'];
                    
                    return _buildCategoryChip(
                      context, 
                      category['name'] ?? '', 
                      isSelected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          apiProductsProvider.setCategory(category['slug'] ?? 'All');
                        }
                      },
                    );
                  },
                );
              },
            ),
          ),
          
          // Products grid with loading/error states
          Expanded(
            child: apiProductsProvider.isLoading
                ? const Center(child: CircularProgressIndicator())
                : apiProductsProvider.error.isNotEmpty
                    ? _buildErrorWidget(apiProductsProvider, isHindi)
                    : apiProductsProvider.products.isEmpty
                        ? _buildEmptyProductsWidget(isHindi)
                        : _buildProductsGrid(apiProductsProvider, isHindi, isDarkMode, gridColumns),
          ),
          
          // Banner Ad
          if (_isAdLoaded)
            SizedBox(
              height: _bannerAd!.size.height.toDouble(),
              width: _bannerAd!.size.width.toDouble(),
              child: AdWidget(ad: _bannerAd!),
            ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(BuildContext context, String label, {bool isSelected = false, Function(bool)? onSelected}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: FilterChip(
        label: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isSelected 
                ? Theme.of(context).primaryColor 
                : (isDarkMode ? Colors.white : Colors.black87),
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        onSelected: onSelected,
        backgroundColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100,
        selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
        checkmarkColor: Theme.of(context).primaryColor,
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: isSelected 
                ? Theme.of(context).primaryColor 
                : Colors.transparent,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget(ApiProductsProvider apiProductsProvider, bool isHindi) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          const SizedBox(height: 16),
          Text(
            isHindi ? 'कुछ गलत हो गया' : 'Something went wrong',
            style: GoogleFonts.montserrat(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            apiProductsProvider.error,
            textAlign: TextAlign.center,
            style: GoogleFonts.lato(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => apiProductsProvider.fetchProducts(),
            icon: const Icon(Icons.refresh),
            label: Text(isHindi ? 'पुनः प्रयास करें' : 'Try Again'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyProductsWidget(bool isHindi) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.shopping_bag_outlined,
            color: Colors.grey,
            size: 60,
          ),
          const SizedBox(height: 16),
          Text(
            isHindi ? 'कोई उत्पाद नहीं मिला' : 'No products found',
            style: GoogleFonts.montserrat(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isHindi
                ? 'बाद में वापस जांचें या अन्य श्रेणी का चयन करें'
                : 'Check back later or select a different category',
            textAlign: TextAlign.center,
            style: GoogleFonts.lato(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsGrid(ApiProductsProvider apiProductsProvider, bool isHindi, bool isDarkMode, int columns) {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        childAspectRatio: 0.7, // Adjusted aspect ratio
        crossAxisSpacing: 8, // Reduced spacing
        mainAxisSpacing: 8, // Reduced spacing
      ),
      itemCount: apiProductsProvider.products.length,
      itemBuilder: (context, index) {
        final product = apiProductsProvider.products[index];
        return _buildProductApiCard(context, product);
      },
    );
  }

  // Create a new method specifically for ProductApi objects
  Widget _buildProductApiCard(BuildContext context, ProductApi product) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final coinsProvider = Provider.of<CoinsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final canAfford = coinsProvider.coins >= product.coinPrice;
    
    return Card(
      elevation: 3,
      shadowColor: isDarkMode ? Colors.black54 : Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
          width: 1,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => ProductApiDetailScreen(productId: product.id),
            ),
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image with badge
            Stack(
              children: [
                Hero(
                  tag: 'product-${product.id}',
                  child: AspectRatio(
                    aspectRatio: 1,
                    child: Image.network(
                      product.image,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                          child: const Icon(
                            Icons.image_not_supported,
                            size: 40,
                            color: Colors.grey,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                // Coin price badge with improved design
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: canAfford 
                          ? Colors.green.shade600
                          : Colors.red.shade600,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.monetization_on,
                          color: Colors.white,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${product.coinPrice}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Out of stock overlay with improved design
                if (!product.inStock)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                          colors: [
                            Colors.black.withOpacity(0.8),
                            Colors.black.withOpacity(0.4),
                          ],
                        ),
                      ),
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.red.shade700,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.3),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            isHindi ? 'स्टॉक में नहीं है' : 'Out of Stock',
                            style: GoogleFonts.montserrat(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            
            // Product name with improved styling
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 12, 10, 8),
              child: Text(
                product.name,
                style: GoogleFonts.montserrat(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
            
            // Spacer to push button to bottom
            const Spacer(),
            
            // Buy button with improved design
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 12),
              child: SizedBox(
                width: double.infinity,
                height: 36,
                child: ElevatedButton(
                  onPressed: canAfford && product.inStock
                      ? () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => ProductApiDetailScreen(productId: product.id),
                            ),
                          );
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: canAfford ? Colors.green.shade600 : Colors.grey.shade400,
                    foregroundColor: Colors.white,
                    disabledBackgroundColor: Colors.grey.shade400,
                    elevation: 0,
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    !product.inStock
                        ? (isHindi ? 'स्टॉक नहीं' : 'Out of Stock')
                        : canAfford
                            ? (isHindi ? 'खरीदें' : 'Buy Now')
                            : (isHindi ? 'और सिक्के' : 'Need More'),
                    style: GoogleFonts.montserrat(
                      fontWeight: FontWeight.bold,
                      fontSize: 12, // Reduced font size
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToOrders(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    
    if (!authProvider.isLoggedIn) {
      // Show login dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(isHindi ? 'लॉगिन आवश्यक' : 'Login Required'),
          content: Text(
            isHindi
                ? 'अपने ऑर्डर देखने के लिए कृपया लॉगिन करें'
                : 'Please login to view your orders',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(isHindi ? 'रद्द करें' : 'Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => const AuthScreen(fromOrders: true),
                  ),
                );
              },
              child: Text(isHindi ? 'लॉगिन करें' : 'Login'),
            ),
          ],
        ),
      );
    } else {
      // Navigate to orders screen
      Navigator.push(
        context,
        MaterialPageRoute(builder: (_) => const OrdersScreen()),
      );
    }
  }

  void _showFilterOptions(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final apiProductsProvider = Provider.of<ApiProductsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isHindi ? 'फ़िल्टर विकल्प' : 'Filter Options',
              style: GoogleFonts.montserrat(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              isHindi ? 'सिक्कों के अनुसार' : 'By Coins',
              style: GoogleFonts.montserrat(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                _buildFilterChip(
                  context, 
                  isHindi ? 'कम सिक्के' : 'Lowest Coins', 
                  apiProductsProvider.sortBy == 'price_low',
                  onSelected: (selected) {
                    if (selected) {
                      apiProductsProvider.setSortBy('price_low');
                    }
                  },
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  context, 
                  isHindi ? 'अधिक सिक्के' : 'Highest Coins', 
                  apiProductsProvider.sortBy == 'price_high',
                  onSelected: (selected) {
                    if (selected) {
                      apiProductsProvider.setSortBy('price_high');
                    }
                  },
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  context, 
                  isHindi ? 'नवीनतम' : 'Newest', 
                  apiProductsProvider.sortBy == 'newest',
                  onSelected: (selected) {
                    if (selected) {
                      apiProductsProvider.setSortBy('newest');
                    }
                  },
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  context, 
                  isHindi ? 'विशेष' : 'Featured', 
                  apiProductsProvider.sortBy == 'featured',
                  onSelected: (selected) {
                    if (selected) {
                      apiProductsProvider.setSortBy('featured');
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(isHindi ? 'लागू करें' : 'Apply'),
              ),
            ),
            const SizedBox(height: 10),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () {
                  apiProductsProvider.clearFilters();
                  Navigator.pop(context);
                },
                child: Text(isHindi ? 'फ़िल्टर साफ़ करें' : 'Clear Filters'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(BuildContext context, String label, bool isSelected, {Function(bool)? onSelected}) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: onSelected,
      backgroundColor: Colors.grey.shade200,
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
      checkmarkColor: Theme.of(context).primaryColor,
      labelStyle: GoogleFonts.montserrat(
        color: isSelected ? Theme.of(context).primaryColor : Colors.black87,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  Future<List<Map<String, String>>> _fetchCategories() async {
  try {
    final response = await http.get(
      Uri.parse('https://katregeneralstores.supremenews.in/api/get_categories.php'),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);

      if (data['success'] == true && data['categories'] is List) {
        return (data['categories'] as List)
            .map<Map<String, String>>((category) => {
                  'slug': category['slug']?.toString() ?? '',
                  'name': category['title']?.toString() ?? '',
                })
            .toList();
      }
    }
    return [];
  } catch (e) {
    print('Error fetching categories: $e');
    return [];
  }
}

}

// Make sure to create this screen if it doesn't exist
// class ProductApiDetailScreen extends StatelessWidget {
//   final String productId;
  
//   const ProductApiDetailScreen({super.key, required this.productId});
  
//   @override
//   Widget build(BuildContext context) {
//     // Implement the product detail screen
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Product Details'),
//       ),
//       body: Center(
//         child: Text('Product ID: $productId'),
//       ),
//     );
//   }
// }
