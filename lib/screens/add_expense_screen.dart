import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rupify/models/expense.dart';
import 'package:rupify/providers/expense_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:intl/intl.dart';
import 'package:google_fonts/google_fonts.dart';

class AddExpenseScreen extends StatefulWidget {
  final Expense? expense;

  const AddExpenseScreen({super.key, this.expense});

  @override
  State<AddExpenseScreen> createState() => _AddExpenseScreenState();
}

class _AddExpenseScreenState extends State<AddExpenseScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  late DateTime _selectedDate;
  late ExpenseCategory _selectedCategory;
  bool _isLend = false; // Add isLend state

  @override
  void initState() {
    super.initState();
    if (widget.expense != null) {
      _amountController.text = widget.expense!.amount.toString();
      _descriptionController.text = widget.expense!.description;
      _selectedDate = widget.expense!.date;
      _selectedCategory = widget.expense!.category;
      _isLend = widget.expense!.isLend; // Initialize from existing expense
    } else {
      _selectedDate = DateTime.now();
      _selectedCategory = ExpenseCategory.others;
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            textTheme: const TextTheme(
              bodyLarge: TextStyle(fontSize: 18),
              bodyMedium: TextStyle(fontSize: 16),
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _saveExpense() {
    if (_formKey.currentState!.validate()) {
      final amount = double.parse(_amountController.text.replaceAll(',', ''));
      final description = _descriptionController.text;
      
      final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
      
      if (widget.expense == null) {
        // Add new expense
        final newExpense = Expense(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          amount: amount,
          description: description,
          date: _selectedDate,
          category: _selectedCategory,
          isLend: _isLend, // Include isLend
        );
        
        expenseProvider.addExpense(newExpense);
      } else {
        // Update existing expense
        final updatedExpense = Expense(
          id: widget.expense!.id,
          amount: amount,
          description: description,
          date: _selectedDate,
          category: _selectedCategory,
          notes: widget.expense!.notes,
          isLend: _isLend, // Include isLend
        );
        
        expenseProvider.updateExpense(updatedExpense);
      }
      
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.expense == null
              ? (isHindi ? 'नया खर्च जोड़ें' : 'Add New Expense')
              : (isHindi ? 'खर्च संपादित करें' : 'Edit Expense'),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Amount field
              Text(
                isHindi ? 'राशि (₹)' : 'Amount (₹)',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  prefixText: '₹ ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  hintText: isHindi ? 'राशि दर्ज करें' : 'Enter amount',
                ),
                style: const TextStyle(fontSize: 18),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return isHindi ? 'राशि आवश्यक है' : 'Amount is required';
                  }
                  if (double.tryParse(value.replaceAll(',', '')) == null) {
                    return isHindi ? 'वैध राशि दर्ज करें' : 'Enter a valid amount';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 20),
              
              // Description field
              Text(
                isHindi ? 'विवरण' : 'Description',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  hintText: isHindi ? 'विवरण दर्ज करें' : 'Enter description',
                ),
                style: const TextStyle(fontSize: 18),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return isHindi ? 'विवरण आवश्यक है' : 'Description is required';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 20),
              
              // Date picker
              Text(
                isHindi ? 'दिनांक' : 'Date',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              InkWell(
                onTap: () => _selectDate(context),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat('dd MMM yyyy').format(_selectedDate),
                        style: const TextStyle(fontSize: 18),
                      ),
                      const Icon(Icons.calendar_today),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Category selector
              Text(
                isHindi ? 'श्रेणी' : 'Category',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Container(
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: GridView.builder(
                  padding: const EdgeInsets.all(8),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 5,
                    childAspectRatio: 1,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: ExpenseCategory.values.length,
                  itemBuilder: (context, index) {
                    final category = ExpenseCategory.values[index];
                    final isSelected = _selectedCategory == category;
                    
                    return InkWell(
                      onTap: () {
                        setState(() {
                          _selectedCategory = category;
                        });
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: isSelected
                              ? category.color.withOpacity(0.3)
                              : Colors.transparent,
                          border: Border.all(
                            color: isSelected ? category.color : Colors.grey,
                            width: isSelected ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              category.icon,
                              color: category.color,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 8),
              Center(
                child: Text(
                  isHindi
                      ? _selectedCategory.hindiName
                      : _selectedCategory.displayName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _selectedCategory.color,
                  ),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Add Lend checkbox after category selector
              Row(
                children: [
                  Checkbox(
                    value: _isLend,
                    onChanged: (value) {
                      setState(() {
                        _isLend = value ?? false;
                      });
                    },
                  ),
                  Text(
                    isHindi ? 'क्या यह उधार है?' : 'Is this a lend?',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // Save button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _saveExpense,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    isHindi ? 'सहेजें' : 'Save',
                    style: const TextStyle(fontSize: 18),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
