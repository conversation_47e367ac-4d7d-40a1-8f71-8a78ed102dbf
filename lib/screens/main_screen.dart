import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:rupify/providers/coins_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/screens/check_in_screen.dart';
import 'package:rupify/screens/products_screen.dart';
import 'package:rupify/screens/todo_screen.dart';
import 'package:rupify/screens/settings_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;
  
  static final List<Widget> _screens = [
    const CheckInScreen(),
    const ProductsScreen(),
    const TodoScreen(),
    const SettingsScreen(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final coinsProvider = Provider.of<CoinsProvider>(context);
    final isHindi = settingsProvider.isHindi;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF36916D).withOpacity(0.1),
        title: Text(
          _selectedIndex == 0 
              ? (isHindi ? 'दैनिक चेक-इन' : 'Daily Check-in')
              : _selectedIndex == 1
                  ? (isHindi ? 'रिवॉर्ड स्टोर' : 'Rewards Store')
                  : _selectedIndex == 2
                      ? (isHindi ? 'मेरे टूडू' : 'My Todos')
                      : (isHindi ? 'सेटिंग्स' : 'Settings'),
          style: TextStyle(color: const Color(0xFF36916D)),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4.0),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.amber, width: 1),
            ),
            child: Row(
              children: [
                Image.asset(
                  'assets/images/icon.png',
                  height: 20,
                  width: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  '${coinsProvider.coins}',
                  style: GoogleFonts.montserrat(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: _screens[_selectedIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: BottomNavigationBar(
            items: <BottomNavigationBarItem>[
              BottomNavigationBarItem(
                icon: const Icon(Icons.calendar_today),
                label: isHindi ? 'चेक-इन' : 'Check-in',
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.shopping_cart),
                label: isHindi ? 'रिवॉर्ड्स' : 'Rewards',
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.check_circle_outline),
                label: isHindi ? 'टूडू' : 'Todo',
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.settings),
                label: isHindi ? 'सेटिंग्स' : 'Settings',
              ),
            ],
            currentIndex: _selectedIndex,
            selectedItemColor: const Color(0xFF36916D),
            unselectedItemColor: Colors.grey,
            elevation: 0,
            onTap: _onItemTapped,
            type: BottomNavigationBarType.fixed,
          ),
        ),
      ),
    );
  }
}
