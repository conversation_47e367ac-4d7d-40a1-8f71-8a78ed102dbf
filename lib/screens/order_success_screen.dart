import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:rupify/models/order.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/screens/orders_screen.dart';

class OrderSuccessScreen extends StatelessWidget {
  final Order order;

  const OrderSuccessScreen({
    super.key,
    required this.order,
  });

  String _getEstimatedDeliveryDate(bool isHindi) {
    final now = DateTime.now();
    final deliveryDate = DateTime(now.year, now.month, now.day + 7);
    
    // Format date as DD/MM/YYYY
    return '${deliveryDate.day}/${deliveryDate.month}/${deliveryDate.year}';
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isHindi ? 'ऑर्डर सफल' : 'Order Success',
          style: GoogleFonts.montserrat(fontWeight: FontWeight.bold),
        ),
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 80,
                    ),
                    const SizedBox(height: 24),
                    Text(
                      isHindi ? 'ऑर्डर सफल!' : 'Order Successful!',
                      style: GoogleFonts.montserrat(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isHindi
                          ? 'आपका ऑर्डर सफलतापूर्वक प्लेस किया गया है'
                          : 'Your order has been successfully placed',
                      textAlign: TextAlign.center,
                      style: GoogleFonts.lato(
                        fontSize: 16,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 32),
                    
                    // Order Details
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              isHindi ? 'ऑर्डर विवरण' : 'Order Details',
                              style: GoogleFonts.montserrat(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 16),
                            _buildOrderDetail(
                              context,
                              isHindi ? 'ऑर्डर आईडी' : 'Order ID',
                              '#${order.id.substring(order.id.length - 6)}',
                            ),
                            const Divider(height: 24),
                            _buildOrderDetail(
                              context,
                              isHindi ? 'तारीख' : 'Date',
                              DateTime.parse(order.date).toString().substring(0, 16),
                            ),
                            const Divider(height: 24),
                            _buildOrderDetail(
                              context,
                              isHindi ? 'कुल राशि' : 'Total Amount',
                              '₹${order.totalAmount.toStringAsFixed(2)}',
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Delivery Details
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              isHindi ? 'डिलीवरी विवरण' : 'Delivery Details',
                              style: GoogleFonts.montserrat(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 16),
                            _buildOrderDetail(
                              context,
                              isHindi ? 'नाम' : 'Name',
                              order.customerName,
                            ),
                            const Divider(height: 24),
                            _buildOrderDetail(
                              context,
                              isHindi ? 'फोन' : 'Phone',
                              order.phonePrimary,
                            ),
                            if (order.phoneAlternate != null) ...[
                              const Divider(height: 24),
                              _buildOrderDetail(
                                context,
                                isHindi ? 'वैकल्पिक फोन' : 'Alternate Phone',
                                order.phoneAlternate!,
                              ),
                            ],
                            const Divider(height: 24),
                            _buildOrderDetail(
                              context,
                              isHindi ? 'पता' : 'Address',
                              order.address,
                            ),
                            const Divider(height: 24),
                            _buildOrderDetail(
                              context,
                              isHindi ? 'अनुमानित डिलीवरी' : 'Estimated Delivery',
                              _getEstimatedDeliveryDate(isHindi),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // Thank You Message
                    Text(
                      isHindi ? 'हमारे साथ खरीदारी करने के लिए धन्यवाद!' : 'Thank you for shopping with us!',
                      textAlign: TextAlign.center,
                      style: GoogleFonts.lato(
                        fontSize: 16,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  // Use pushAndRemoveUntil to clear the navigation stack
                  // This prevents issues when going back from orders screen
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (_) => const OrdersScreen(),
                    ),
                    (route) => false, // Remove all previous routes
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  isHindi ? 'मेरे ऑर्डर देखें' : 'View My Orders',
                  style: GoogleFonts.montserrat(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  // Navigate back to home or shop
                  Navigator.of(context).popUntil((route) => route.isFirst);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  isHindi ? 'होम' : 'Home',
                  style: GoogleFonts.montserrat(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderDetail(BuildContext context, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
