import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rupify/providers/expense_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/providers/auth_provider.dart';
import 'package:rupify/screens/auth_screen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _budgetController = TextEditingController();
  String _appVersion = '';
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;
  
  @override
  void initState() {
    super.initState();
    _loadAppVersion();
    _loadBannerAd();
    
    // Initialize budget controller with current value
    final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
    _budgetController.text = expenseProvider.monthlyBudget.toString();
  }
  
  @override
  void dispose() {
    _budgetController.dispose();
    _bannerAd?.dispose();
    super.dispose();
  }
  
  void _loadBannerAd() {
    _bannerAd = BannerAd(
      adUnitId: 'ca-app-pub-8904820275824881/**********', // Test ad unit ID
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (_) {
          setState(() {
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
        },
      ),
    );

    _bannerAd?.load();
  }
  
  Future<void> _loadAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appVersion = packageInfo.version;
    });
  }

  Future<void> _selectReminderTime(BuildContext context) async {
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final currentTime = settingsProvider.reminderTime;
    
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: currentTime,
    );
    
    if (pickedTime != null && pickedTime != currentTime) {
      settingsProvider.setReminderTime(pickedTime);
      // Remove notification scheduling code
    }
  }

  Future<void> _updateBudget() async {
    final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
    final newBudget = double.tryParse(_budgetController.text);
    
    if (newBudget != null && newBudget > 0) {
      await expenseProvider.setMonthlyBudget(newBudget);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<SettingsProvider>(context, listen: false).isHindi
                  ? 'मासिक बजट अपडेट किया गया'
                  : 'Monthly budget updated',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isHindi ? 'सेटिंग्स' : 'Settings'),
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                // User Profile Card
                Card(
                  child: Column(
                    children: [
                      ListTile(
                        title: Text(
                          isHindi ? 'उपयोगकर्ता प्रोफ़ाइल' : 'User Profile',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        leading: const Icon(Icons.person_outline),
                      ),
                      const Divider(),
                      if (authProvider.isLoggedIn && authProvider.currentUser != null) ...[
                        // Show user details if logged in
                        ListTile(
                          title: Text(isHindi ? 'नाम' : 'Name'),
                          subtitle: Text(authProvider.currentUser?.name ?? ''),
                        ),
                        if (authProvider.currentUser?.phone != null)
                          ListTile(
                            title: Text(isHindi ? 'फोन' : 'Phone'),
                            subtitle: Text(authProvider.currentUser?.phone ?? ''),
                          ),
                        if (authProvider.currentUser?.email != null && authProvider.currentUser!.email!.isNotEmpty)
                          ListTile(
                            title: Text(isHindi ? 'ईमेल' : 'Email'),
                            subtitle: Text(authProvider.currentUser?.email ?? ''),
                          ),
                        if (authProvider.currentUser?.referralCode != null)
                          ListTile(
                            title: Text(isHindi ? 'रेफरल कोड' : 'Referral Code'),
                            subtitle: Text(authProvider.currentUser?.referralCode ?? ''),
                          ),
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () async {
                                // Show confirmation dialog
                                final confirmed = await showDialog<bool>(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    title: Text(isHindi ? 'लॉगआउट करें?' : 'Logout?'),
                                    content: Text(
                                      isHindi
                                          ? 'क्या आप वाकई लॉगआउट करना चाहते हैं?'
                                          : 'Are you sure you want to logout?'
                                    ),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Navigator.pop(context, false),
                                        child: Text(isHindi ? 'रद्द करें' : 'Cancel'),
                                      ),
                                      ElevatedButton(
                                        onPressed: () => Navigator.pop(context, true),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.red,
                                          foregroundColor: Colors.white,
                                        ),
                                        child: Text(isHindi ? 'लॉगआउट' : 'Logout'),
                                      ),
                                    ],
                                  ),
                                ) ?? false;
                                
                                if (confirmed) {
                                  await authProvider.logout();
                                  if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          isHindi ? 'लॉगआउट सफल' : 'Logout successful'
                                        ),
                                        backgroundColor: Colors.green,
                                      ),
                                    );
                                  }
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                foregroundColor: Colors.white,
                              ),
                              child: Text(isHindi ? 'लॉगआउट' : 'Logout'),
                            ),
                          ),
                        ),
                      ] else ...[
                        // Show login button if not logged in
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              Text(
                                isHindi
                                    ? 'अपने खाते में लॉगिन करें'
                                    : 'Login to your account',
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (_) => const AuthScreen(fromOrders: false),
                                      ),
                                    );
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Theme.of(context).primaryColor,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: Text(isHindi ? 'लॉगिन करें' : 'Login'),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Monthly Budget
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isHindi ? 'मासिक बजट' : 'Monthly Budget',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _budgetController,
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  prefixText: '₹ ',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            ElevatedButton(
                              onPressed: _updateBudget,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                              ),
                              child: Text(isHindi ? 'अपडेट' : 'Update'),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          isHindi
                              ? 'वर्तमान: ${expenseProvider.formatCurrency(expenseProvider.monthlyBudget)}'
                              : 'Current: ${expenseProvider.formatCurrency(expenseProvider.monthlyBudget)}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Language Settings
                Card(
                  child: ListTile(
                    title: Text(isHindi ? 'भाषा' : 'Language'),
                    subtitle: Text(isHindi ? 'हिंदी' : 'English'),
                    trailing: Switch(
                      value: isHindi,
                      onChanged: (value) {
                        settingsProvider.toggleLanguage();
                      },
                      activeColor: Colors.green,
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Notification Settings
                Card(
                  child: Column(
                    children: [
                      ListTile(
                        title: Text(isHindi ? 'दैनिक रिमाइंडर' : 'Daily Reminder'),
                        subtitle: Text(
                          isHindi
                              ? 'खर्च दर्ज करने के लिए दैनिक अनुस्मारक'
                              : 'Daily reminder to log expenses',
                        ),
                        trailing: Switch(
                          value: settingsProvider.isDailyReminderEnabled,
                          onChanged: (value) async {
                            await settingsProvider.setDailyReminder(value);
                            // Remove notification code
                          },
                          activeColor: Colors.green,
                        ),
                      ),
                      if (settingsProvider.isDailyReminderEnabled)
                        ListTile(
                          title: Text(isHindi ? 'रिमाइंडर समय' : 'Reminder Time'),
                          subtitle: Text(
                            '${settingsProvider.reminderTime.hour}:${settingsProvider.reminderTime.minute.toString().padLeft(2, '0')}',
                          ),
                          trailing: const Icon(Icons.access_time),
                          onTap: () => _selectReminderTime(context),
                        ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // App Info
                Card(
                  child: Column(
                    children: [
                      ListTile(
                        title: Text(isHindi ? 'ऐप के बारे में' : 'About App'),
                        subtitle: Text(
                          isHindi
                              ? 'रुपिफाई - आपका व्यक्तिगत खर्च ट्रैकर'
                              : 'Rupify - Your Personal Expense Tracker',
                        ),
                      ),
                      const Divider(),
                      ListTile(
                        title: Text(isHindi ? 'संस्करण' : 'Version'),
                        subtitle: Text(_appVersion),
                      ),
                      ListTile(
                        title: Text(isHindi ? 'डेवलपर से संपर्क करें' : 'Contact Developer'),
                        trailing: const Icon(Icons.email),
                        onTap: () async {
                          final Uri emailUri = Uri(
                            scheme: 'mailto',
                            path: '<EMAIL>',
                            query: 'subject=rupify Feedback',
                          );
                          
                          if (await canLaunchUrl(emailUri)) {
                            await launchUrl(emailUri);
                          }
                        },
                      ),
                      ListTile(
                        title: Text(isHindi ? 'रेटिंग दें' : 'Rate App'),
                        trailing: const Icon(Icons.star),
                        onTap: () async {
                          // Replace with actual store URL
                          final Uri storeUri = Uri.parse('https://play.google.com/store/apps');
                          
                          if (await canLaunchUrl(storeUri)) {
                            await launchUrl(storeUri);
                          }
                        },
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // App restoration info
                Center(
                  child: Text(
                    isHindi
                        ? 'आपका डेटा स्वचालित रूप से सहेजा जाता है'
                        : 'Your data is automatically saved',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ),
                
                // Theme settings section
                Card(
                  child: Column(
                    children: [
                      ListTile(
                        title: Text(isHindi ? 'थीम सेटिंग्स' : 'Theme Settings'),
                        subtitle: Text(
                          isHindi ? 'ऐप का रंग थीम बदलें' : 'Change app color theme',
                        ),
                      ),
                      const Divider(),
                      RadioListTile<ThemeMode>(
                        title: Text(isHindi ? 'सिस्टम डिफ़ॉल्ट' : 'System Default'),
                        value: ThemeMode.system,
                        groupValue: settingsProvider.themeMode,
                        onChanged: (ThemeMode? value) {
                          if (value != null) {
                            settingsProvider.setThemeMode(value);
                          }
                        },
                      ),
                      RadioListTile<ThemeMode>(
                        title: Text(isHindi ? 'लाइट थीम' : 'Light Theme'),
                        value: ThemeMode.light,
                        groupValue: settingsProvider.themeMode,
                        onChanged: (ThemeMode? value) {
                          if (value != null) {
                            settingsProvider.setThemeMode(value);
                          }
                        },
                      ),
                      RadioListTile<ThemeMode>(
                        title: Text(isHindi ? 'डार्क थीम' : 'Dark Theme'),
                        value: ThemeMode.dark,
                        groupValue: settingsProvider.themeMode,
                        onChanged: (ThemeMode? value) {
                          if (value != null) {
                            settingsProvider.setThemeMode(value);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Banner Ad
          if (_isAdLoaded)
            SizedBox(
              height: _bannerAd!.size.height.toDouble(),
              width: _bannerAd!.size.width.toDouble(),
              child: AdWidget(ad: _bannerAd!),
            ),
        ],
      ),
    );
  }
}
