import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:rupify/models/expense.dart';
import 'package:rupify/providers/expense_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:intl/intl.dart';

class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen> {
  String _timeRange = 'monthly';

  List<Expense> _getFilteredExpenses(ExpenseProvider provider) {
    switch (_timeRange) {
      case 'weekly':
        return provider.getWeeklyExpenses();
      case 'monthly':
        return provider.getMonthlyExpenses();
      case 'yearly':
        return provider.getYearlyExpenses();
      default:
        return provider.expenses;
    }
  }

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    final filteredExpenses = _getFilteredExpenses(expenseProvider);
    final categoryTotals = expenseProvider.getCategoryTotals(filteredExpenses);
    
    // Sort categories by amount (highest first)
    final sortedCategories = categoryTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    final totalSpent = categoryTotals.values.fold(0.0, (sum, amount) => sum + amount);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isHindi ? 'विश्लेषण' : 'Analytics'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time range selector
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildTimeRangeButton(
                  isHindi ? 'साप्ताहिक' : 'Weekly',
                  'weekly',
                ),
                const SizedBox(width: 8),
                _buildTimeRangeButton(
                  isHindi ? 'मासिक' : 'Monthly',
                  'monthly',
                ),
                const SizedBox(width: 8),
                _buildTimeRangeButton(
                  isHindi ? 'वार्षिक' : 'Yearly',
                  'yearly',
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Total spent
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isHindi ? 'कुल खर्च' : 'Total Spent',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      expenseProvider.formatCurrency(totalSpent),
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade800,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getTimeRangeText(isHindi),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Pie chart
            if (totalSpent > 0) ...[
              Text(
                isHindi ? 'श्रेणी वार खर्च' : 'Spending by Category',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 250,
                child: PieChart(
                  PieChartData(
                    sections: _buildPieChartSections(sortedCategories, totalSpent),
                    centerSpaceRadius: 40,
                    sectionsSpace: 2,
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Category breakdown
              Text(
                isHindi ? 'श्रेणी विवरण' : 'Category Breakdown',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              ...sortedCategories.map((entry) {
                final category = entry.key;
                final amount = entry.value;
                final percentage = (amount / totalSpent * 100).toStringAsFixed(1);
                
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: category.color,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          category.icon,
                          color: Colors.white,
                          size: 14,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          isHindi ? category.hindiName : category.displayName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Text(
                        expenseProvider.formatCurrency(amount),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 45,
                        child: Text(
                          '$percentage%',
                          textAlign: TextAlign.right,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ] else ...[
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.bar_chart,
                        size: 64,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        isHindi
                            ? 'इस अवधि के लिए कोई खर्च नहीं मिला'
                            : 'No expenses found for this period',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimeRangeButton(String label, String value) {
    final isSelected = _timeRange == value;
    
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _timeRange = value;
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? Colors.green : Colors.grey.shade200,
        foregroundColor: isSelected ? Colors.white : Colors.black,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
      child: Text(label),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(
    List<MapEntry<ExpenseCategory, double>> sortedCategories,
    double totalSpent,
  ) {
    return sortedCategories.map((entry) {
      final category = entry.key;
      final amount = entry.value;
      final percentage = amount / totalSpent;
      
      return PieChartSectionData(
        color: category.color,
        value: amount,
        title: '${(percentage * 100).toStringAsFixed(1)}%',
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  String _getTimeRangeText(bool isHindi) {
    final now = DateTime.now();
    
    switch (_timeRange) {
      case 'weekly':
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        final endOfWeek = startOfWeek.add(const Duration(days: 6));
        return isHindi
            ? '${DateFormat('d MMM').format(startOfWeek)} - ${DateFormat('d MMM').format(endOfWeek)}'
            : '${DateFormat('d MMM').format(startOfWeek)} - ${DateFormat('d MMM').format(endOfWeek)}';
      case 'monthly':
        return isHindi
            ? DateFormat('MMMM yyyy').format(now)
            : DateFormat('MMMM yyyy').format(now);
      case 'yearly':
        return isHindi
            ? DateFormat('yyyy').format(now)
            : DateFormat('yyyy').format(now);
      default:
        return '';
    }
  }
}
