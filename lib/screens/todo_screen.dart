import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:reorderables/reorderables.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:csv/csv.dart';
import 'package:rupify/utils/notification_service.dart';

import '../providers/todo_provider.dart';
import '../providers/auth_provider.dart';

class TodoScreen extends StatefulWidget {
  const TodoScreen({Key? key}) : super(key: key);

  @override
  _TodoScreenState createState() => _TodoScreenState();
}

class _TodoScreenState extends State<TodoScreen> {
  DateTime _selectedDate = DateTime.now();
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;
  Map<String, int> _todoPositions = {};
  bool _isUploading = false;
  bool _isDownloading = false;
  List<TodoItem> _todos = [];
  
  @override
  void initState() {
    super.initState();
    _loadTodos();
    _loadBannerAd();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  void _loadBannerAd() {
    _bannerAd = BannerAd(
      adUnitId: 'ca-app-pub-8904820275824881/**********', // Test ad unit ID
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (_) {
          setState(() {
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
        },
      ),
    );

    _bannerAd?.load();
  }
  
  Future<void> _loadTodos() async {
    final prefs = await SharedPreferences.getInstance();
    final todosJson = prefs.getStringList('todos') ?? [];
    final positionsJson = prefs.getString('todoPositions') ?? '{}';
    
    setState(() {
      _todos = todosJson
          .map((json) => TodoItem.fromJson(jsonDecode(json)))
          .toList();
      _todoPositions = Map<String, int>.from(jsonDecode(positionsJson));
      
      // Sort todos based on positions
      _todos.sort((a, b) => 
        (_todoPositions[a.id] ?? 999999).compareTo(_todoPositions[b.id] ?? 999999)
      );
    });
  }
  
  Future<void> _saveTodos() async {
    final prefs = await SharedPreferences.getInstance();
    final todosJson = _todos
        .map((todo) => jsonEncode(todo.toJson()))
        .toList();
    
    await prefs.setStringList('todos', todosJson);
  }
  
  Future<void> _saveTodoPositions() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('todoPositions', jsonEncode(_todoPositions));
  }
  
  Future<void> _addTodo(String title, String description, TimeOfDay time) async {
    print('Adding todo: $title for time: ${time.format(context)}'); // Debug log
    
    final newTodo = TodoItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      date: _selectedDate,
      time: time,
      isCompleted: false,
    );
    
    // Add to list and save
    setState(() {
      _todos.add(newTodo);
    });
    await _saveTodos();
    
    // Schedule notification
    try {
      print('Attempting to schedule notification for todo: ${newTodo.title}'); // Debug log
      await NotificationService().scheduleTodoNotification(
        newTodo.id,
        newTodo.title,
        newTodo.description.isEmpty ? 'Time to complete your todo!' : newTodo.description,
        newTodo.date,
        newTodo.time,
      );
      print('Notification scheduled successfully'); // Debug log
      
    } catch (e) {
      print('Error scheduling notification: $e');
      if (!context.mounted) return;
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Error'),
          content: Text('Error scheduling notification: $e'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }
  
  Future<void> _updateTodo(String id, String title, String description, TimeOfDay time, bool isCompleted) async {
    setState(() {
      final todoIndex = _todos.indexWhere((todo) => todo.id == id);
      if (todoIndex != -1) {
        _todos[todoIndex] = _todos[todoIndex].copyWith(
          title: title,
          description: description,
          time: time,
          isCompleted: isCompleted,
        );
      }
    });
    
    await _saveTodos();
    
    // Cancel existing notification and reschedule if not completed
    await NotificationService().cancelNotification(id);
    
    if (!isCompleted) {
      final todo = _todos.firstWhere((todo) => todo.id == id);
      await NotificationService().scheduleTodoNotification(
        todo.id,
        todo.title,
        todo.description.isEmpty ? 'Time to complete your todo!' : todo.description,
        todo.date,
        todo.time,
      );
    }
  }
  
  Future<void> _toggleTodoStatus(String id) async {
    setState(() {
      final todoIndex = _todos.indexWhere((todo) => todo.id == id);
      if (todoIndex != -1) {
        _todos[todoIndex] = _todos[todoIndex].copyWith(
          isCompleted: !_todos[todoIndex].isCompleted,
        );
        
        // Cancel notification if marked as completed
        if (_todos[todoIndex].isCompleted) {
          NotificationService().cancelNotification(id);
        } else {
          // Reschedule notification if marked as not completed
          final todo = _todos[todoIndex];
          NotificationService().scheduleTodoNotification(
            todo.id,
            todo.title,
            todo.description.isEmpty ? 'Time to complete your todo!' : todo.description,
            todo.date,
            todo.time,
          );
        }
      }
    });
    
    await _saveTodos();
  }
  
  Future<void> _deleteTodo(String id) async {
    setState(() {
      _todos.removeWhere((todo) => todo.id == id);
    });
    
    await _saveTodos();
    
    // Cancel notification
    await NotificationService().cancelNotification(id);
  }
  
  void _changeDate(int days) {
    setState(() {
      _selectedDate = _selectedDate.add(Duration(days: days));
    });
  }
  
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            textTheme: const TextTheme(
              bodyLarge: TextStyle(fontSize: 18),
              bodyMedium: TextStyle(fontSize: 16),
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }
  
  void _showAddTodoDialog() {
    final TextEditingController titleController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    final theme = Theme.of(context);
    
    // Default time to current time rounded to nearest 15 minutes
    TimeOfDay selectedTime = TimeOfDay.now();
    int minute = selectedTime.minute;
    minute = (minute ~/ 15) * 15; // Round to nearest 15 minutes
    selectedTime = TimeOfDay(hour: selectedTime.hour, minute: minute);
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Icon(Icons.add_task, color: theme.colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                isHindi ? 'नया टूडू जोड़ें' : 'Add New Todo',
                style: GoogleFonts.montserrat(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'शीर्षक' : 'Title',
                    hintText: isHindi ? 'टूडू का शीर्षक' : 'Todo title',
                    prefixIcon: const Icon(Icons.title),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  autofocus: true,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'विवरण' : 'Description',
                    hintText: isHindi ? 'टूडू का विवरण' : 'Todo description',
                    prefixIcon: const Icon(Icons.description),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                InkWell(
                  onTap: () async {
                    final TimeOfDay? picked = await showTimePicker(
                      context: context,
                      initialTime: selectedTime,
                    );
                    if (picked != null) {
                      setState(() {
                        selectedTime = picked;
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.access_time),
                        const SizedBox(width: 8),
                        Text(
                          isHindi ? 'समय' : 'Time',
                          style: TextStyle(
                            color: Colors.grey[700],
                            fontSize: 16,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          selectedTime.format(context),
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.close),
              label: Text(isHindi ? 'रद्द करें' : 'Cancel'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                if (titleController.text.isNotEmpty) {
                  // First close the dialog
                  Navigator.pop(context);
                  
                  // Then add the todo
                  _addTodo(
                    titleController.text,
                    descriptionController.text,
                    selectedTime,
                  ).then((_) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(isHindi ? 'टूडू जोड़ा गया' : 'Todo added'),
                        backgroundColor: Colors.green,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  });
                }
              },
              icon: const Icon(Icons.add),
              label: Text(isHindi ? 'जोड़ें' : 'Add'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  void _showEditTodoDialog(TodoItem todo) {
    final TextEditingController titleController = TextEditingController(text: todo.title);
    final TextEditingController descriptionController = TextEditingController(text: todo.description);
    bool isCompleted = todo.isCompleted;
    TimeOfDay selectedTime = todo.time;
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    final theme = Theme.of(context);
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Icon(Icons.edit_note, color: theme.colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                isHindi ? 'टूडू संपादित करें' : 'Edit Todo',
                style: GoogleFonts.montserrat(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'शीर्षक' : 'Title',
                    prefixIcon: const Icon(Icons.title),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'विवरण' : 'Description',
                    prefixIcon: const Icon(Icons.description),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                InkWell(
                  onTap: () async {
                    final TimeOfDay? picked = await showTimePicker(
                      context: context,
                      initialTime: selectedTime,
                    );
                    if (picked != null) {
                      setState(() {
                        selectedTime = picked;
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.access_time),
                        const SizedBox(width: 8),
                        Text(
                          isHindi ? 'समय' : 'Time',
                          style: TextStyle(
                            color: Colors.grey[700],
                            fontSize: 16,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          selectedTime.format(context),
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: Text(isHindi ? 'पूरा हुआ' : 'Completed'),
                  value: isCompleted,
                  onChanged: (value) {
                    setState(() {
                      isCompleted = value;
                    });
                  },
                  activeColor: theme.colorScheme.primary,
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
          actions: [
            TextButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.close),
              label: Text(isHindi ? 'रद्द करें' : 'Cancel'),
            ),
            TextButton.icon(
              onPressed: () {
                _deleteTodo(todo.id);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(isHindi ? 'टूडू हटा दिया गया' : 'Todo deleted'),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              icon: const Icon(Icons.delete),
              label: Text(isHindi ? 'हटाएं' : 'Delete'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                if (titleController.text.isNotEmpty) {
                  _updateTodo(
                    todo.id,
                    titleController.text,
                    descriptionController.text,
                    selectedTime,
                    isCompleted,
                  );
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(isHindi ? 'टूडू अपडेट किया गया' : 'Todo updated'),
                      backgroundColor: Colors.green,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                }
              },
              icon: const Icon(Icons.save),
              label: Text(isHindi ? 'अपडेट करें' : 'Update'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ));
  }

  List<TodoItem> _getTodosForSelectedDate() {
    return _todos.where((todo) {
      final todoDate = DateTime(
        todo.date.year,
        todo.date.month,
        todo.date.day,
      );
      final selectedDate = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
      );
      return todoDate.isAtSameMomentAs(selectedDate);
    }).toList();
  }

  Future<void> _exportTodosToCSV() async {
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    final todosForSelectedDate = _getTodosForSelectedDate();
    
    if (todosForSelectedDate.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isHindi ? 'इस दिन के लिए कोई टूडू नहीं है' : 'No todos for this day'),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }
    
    // Create CSV data
    List<List<dynamic>> csvData = [
      // Header row
      ['Title', 'Description', 'Time', 'Completed']
    ];
    
    // Add data rows
    for (var todo in todosForSelectedDate) {
      csvData.add([
        todo.title,
        todo.description,
        todo.time.format(context),
        todo.isCompleted ? 'Yes' : 'No',
      ]);
    }
    
    // Convert to CSV string
    String csv = const ListToCsvConverter().convert(csvData);
    
    // Get temporary directory
    final directory = await getTemporaryDirectory();
    final formattedDate = DateFormat('yyyy-MM-dd').format(_selectedDate);
    final path = '${directory.path}/todos_$formattedDate.csv';
    
    // Write to file
    final File file = File(path);
    await file.writeAsString(csv);
    
    // Share the file
    await Share.shareXFiles(
      [XFile(path)],
      subject: isHindi 
          ? '$formattedDate के लिए टूडू सूची' 
          : 'Todo List for $formattedDate',
    );
  }

  @override
  Widget build(BuildContext context) {
    final todoProvider = Provider.of<TodoProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isHindi = settingsProvider.isHindi;
    final theme = Theme.of(context);
    
    // Get todos for the selected date
    final todosForSelectedDate = _getTodosForSelectedDate();
    
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
        title: Text(
          isHindi ? 'मेरे टूडू' : 'My Todos',
          style: theme.textTheme.titleLarge?.copyWith(color: theme.colorScheme.primary),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.file_download, color: theme.colorScheme.primary),
            onPressed: _exportTodosToCSV,
            tooltip: isHindi ? 'CSV निर्यात करें' : 'Export CSV',
          ),
          IconButton(
            icon: Icon(Icons.calendar_today, color: theme.colorScheme.primary),
            onPressed: () => _selectDate(context),
            tooltip: isHindi ? 'तारीख चुनें' : 'Select Date',
          ),
          // Upload button
          IconButton(
            icon: _isUploading 
                ? const SizedBox(
                    width: 20, 
                    height: 20, 
                    child: CircularProgressIndicator(strokeWidth: 2)
                  )
                : const Icon(Icons.cloud_upload,color: Color(0xFF36916D),),
            tooltip: isHindi ? 'अपलोड करें' : 'Upload',
            onPressed: _isUploading 
                ? null 
                : () => _uploadTodos(context, todoProvider, authProvider),
          ),
          
          // Download button
          IconButton(
            icon: _isDownloading 
                ? const SizedBox(
                    width: 20, 
                    height: 20, 
                    child: CircularProgressIndicator(strokeWidth: 2)
                  )
                : const Icon(Icons.cloud_download,color: Color(0xFF36916D),),
            tooltip: isHindi ? 'डाउनलोड करें' : 'Download',
            onPressed: _isDownloading 
                ? null 
                : () => _downloadTodos(context, todoProvider, authProvider),
          ),
        ],
      ),
      body: Column(
        children: [
          // Date navigation bar
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: theme.cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  onPressed: () => _changeDate(-1),
                ),
                GestureDetector(
                  onTap: () => _selectDate(context),
                  child: Column(
                    children: [
                      Text(
                        DateFormat('EEEE').format(_selectedDate),
                        style: GoogleFonts.montserrat(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        DateFormat('dd MMM yyyy').format(_selectedDate),
                        style: GoogleFonts.montserrat(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  onPressed: () => _changeDate(1),
                ),
              ],
            ),
          ),
          
          // Todo list
          Expanded(
            child: todosForSelectedDate.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          isHindi ? 'इस दिन के लिए कोई टूडू नहीं' : 'No todos for this day',
                          style: GoogleFonts.montserrat(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: _showAddTodoDialog,
                          icon: const Icon(Icons.add),
                          label: Text(isHindi ? 'टूडू जोड़ें' : 'Add Todo'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF36916D),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                : ReorderableListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: todosForSelectedDate.length,
                    onReorder: (oldIndex, newIndex) {
                      setState(() {
                        // Get the list of todos for the selected date
                        final todosForDate = _getTodosForSelectedDate();

                        // Adjust the newIndex if it's after the removed item
                        if (newIndex > oldIndex) {
                          newIndex -= 1;
                        }

                        // Find the todo being moved
                        final movedTodo = todosForDate[oldIndex];

                        // Find its index in the main _todos list
                        final mainIndex = _todos.indexWhere((t) => t.id == movedTodo.id);

                        // Remove from main list
                        final removed = _todos.removeAt(mainIndex);

                        // Calculate the new index in the main list
                        // Find the todo at newIndex in the filtered list and get its index in the main list
                        int newMainIndex;
                        if (newIndex >= todosForDate.length) {
                          // If moved to the end
                          newMainIndex = _todos.length;
                        } else {
                          final targetTodo = todosForDate[newIndex];
                          newMainIndex = _todos.indexWhere((t) => t.id == targetTodo.id);
                        }

                        // Insert at new position in main list
                        _todos.insert(newMainIndex, removed);

                        // Update positions for all todos for the selected date
                        final updatedTodosForDate = _getTodosForSelectedDate();
                        for (var i = 0; i < updatedTodosForDate.length; i++) {
                          _todoPositions[updatedTodosForDate[i].id] = i;
                        }
                        _saveTodoPositions();
                      });
                    },
                    itemBuilder: (context, index) {
                      final todo = todosForSelectedDate[index];
                      return Dismissible(
                        key: Key(todo.id),
                        background: Container(
                          color: Colors.red,
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.only(right: 16),
                          child: const Icon(
                            Icons.delete,
                            color: Colors.white,
                          ),
                        ),
                        direction: DismissDirection.endToStart,
                        onDismissed: (_) => _deleteTodo(todo.id),
                        child: _buildTodoCard(todo, isHindi, theme),
                      );
                    },
                  ),
          ),
          
          // Banner Ad
          if (_isAdLoaded)
            SizedBox(
              height: _bannerAd!.size.height.toDouble(),
              width: _bannerAd!.size.width.toDouble(),
              child: AdWidget(ad: _bannerAd!),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddTodoDialog,
        backgroundColor: theme.colorScheme.primary,
        child: const Icon(Icons.add),
      ),
    );
  }
Future<void> _uploadTodos(
    BuildContext context, 
    TodoProvider todoProvider,
    AuthProvider authProvider
  ) async {
    final token = authProvider.token;
    
    if (token.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('You need to login first')),
      );
      return;
    }
    
    setState(() {
      _isUploading = true;
    });
    
    try {
      final result = await todoProvider.uploadTodosToServer(token);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Uploaded ${result['count']} of ${result['total']} todos'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Upload failed: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }
  
  Future<void> _downloadTodos(
    BuildContext context, 
    TodoProvider todoProvider,
    AuthProvider authProvider
  ) async {
    final token = authProvider.token;
    
    if (token.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('You need to login first')),
      );
      return;
    }
    
    setState(() {
      _isDownloading = true;
    });
    
    try {
      final result = await todoProvider.fetchTodosFromServer(token);
      
      if (result['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Downloaded ${result['count']} todos'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download failed: ${result['message']}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Download failed: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isDownloading = false;
      });
    }
  }
  
  Future<void> _exportTodos(BuildContext context, TodoProvider todoProvider) async {
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    
    final todos = todoProvider.getTodosForDate(_selectedDate);
    
    if (todos.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isHindi 
              ? 'इस दिन के लिए कोई टूडू नहीं है' 
              : 'No todos for this date'),
        ),
      );
      return;
    }
    
    // Prepare CSV data
    List<List<dynamic>> csvData = [
      // Header row
      ['Title', 'Description', 'Time', 'Completed']
    ];
    
    // Add todo rows
    for (var todo in todos) {
      csvData.add([
        todo.title,
        todo.description,
        todo.time != null 
            ? '${todo.time!.hour.toString().padLeft(2, '0')}:${todo.time!.minute.toString().padLeft(2, '0')}'
            : '',
        todo.isCompleted ? 'Yes' : 'No',
      ]);
    }
    
    // Convert to CSV string
    String csv = const ListToCsvConverter().convert(csvData);
    
    // Get temporary directory
    final directory = await getTemporaryDirectory();
    final formattedDate = DateFormat('yyyy-MM-dd').format(_selectedDate);
    final path = '${directory.path}/todos_$formattedDate.csv';
    
    // Write to file
    final File file = File(path);
    await file.writeAsString(csv);
    
    // Share the file
    await Share.shareXFiles(
      [XFile(path)],
      subject: isHindi 
          ? '$formattedDate के लिए टूडू सूची' 
          : 'Todo List for $formattedDate',
    );
  }
  Widget _buildTodoCard(TodoItem todo, bool isHindi, ThemeData theme) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: todo.isCompleted ? Colors.green.withOpacity(0.3) : Colors.transparent,
          width: 1.5,
        ),
      ),
      child: InkWell(
        onTap: () => _showEditTodoDialog(todo),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            children: [
              // Drag handle for reordering
              ReorderableDragStartListener(
                index: _getTodosForSelectedDate().indexWhere((t) => t.id == todo.id),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                  child: Icon(
                    Icons.drag_handle,
                    color: Colors.grey[400],
                    size: 20,
                  ),
                ),
              ),
              // Priority indicator or completion status
              Container(
                width: 4,
                height: 50,
                decoration: BoxDecoration(
                  color: todo.isCompleted ? Colors.green : theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 12),
              // Checkbox with ripple effect
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () => _toggleTodoStatus(todo.id),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Icon(
                      todo.isCompleted ? Icons.check_circle : Icons.circle_outlined,
                      color: todo.isCompleted ? Colors.green : theme.colorScheme.primary,
                      size: 24,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Todo content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      todo.title,
                      style: GoogleFonts.montserrat(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        decoration: todo.isCompleted ? TextDecoration.lineThrough : null,
                        color: todo.isCompleted ? Colors.grey : null,
                      ),
                    ),
                    if (todo.description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        todo.description,
                        style: GoogleFonts.lato(
                          fontSize: 14,
                          color: todo.isCompleted ? Colors.grey : Colors.grey[700],
                          decoration: todo.isCompleted ? TextDecoration.lineThrough : null,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          todo.time.format(context),
                          style: GoogleFonts.lato(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Edit button
              IconButton(
                icon: Icon(
                  Icons.edit,
                  color: theme.colorScheme.primary.withOpacity(0.7),
                  size: 20,
                ),
                onPressed: () => _showEditTodoDialog(todo),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class TodoItem {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final TimeOfDay time;  // Add time field
  final bool isCompleted;

  TodoItem({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.time,  // Make time required
    required this.isCompleted,
  });

  TodoItem copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? date,
    TimeOfDay? time,  // Add time to copyWith
    bool? isCompleted,
  }) {
    return TodoItem(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      time: time ?? this.time,  // Include time in copyWith
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'date': date.millisecondsSinceEpoch,
      'time': {'hour': time.hour, 'minute': time.minute},  // Store time as map
      'isCompleted': isCompleted,
    };
  }
  
  factory TodoItem.fromJson(Map<String, dynamic> json) {
    // Handle both new format with time and old format without time
    TimeOfDay time;
    if (json['time'] != null) {
      time = TimeOfDay(
        hour: json['time']['hour'],
        minute: json['time']['minute'],
      );
    } else {
      time = const TimeOfDay(hour: 9, minute: 0);  // Default time
    }
    
    return TodoItem(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      date: DateTime.fromMillisecondsSinceEpoch(json['date']),
      time: time,
      isCompleted: json['isCompleted'],
    );
  }
}
