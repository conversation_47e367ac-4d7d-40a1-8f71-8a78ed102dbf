import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:rupify/models/product_api.dart';
import 'package:rupify/providers/api_products_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/providers/coins_provider.dart';
import 'package:rupify/screens/product_api_detail_screen.dart';
import 'package:rupify/screens/orders_screen.dart';

class RewardsProductsScreen extends StatefulWidget {
  const RewardsProductsScreen({super.key});

  @override
  State<RewardsProductsScreen> createState() => _RewardsProductsScreenState();
}

class _RewardsProductsScreenState extends State<RewardsProductsScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch products when screen loads
    Future.microtask(() => 
      Provider.of<ApiProductsProvider>(context, listen: false).fetchProducts()
    );
  }

  @override
  Widget build(BuildContext context) {
    final productsProvider = Provider.of<ApiProductsProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final coinsProvider = Provider.of<CoinsProvider>(context);
    final isHindi = settingsProvider.isHindi;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isHindi ? 'रिवॉर्ड्स शॉप' : 'Rewards Shop',
          style: GoogleFonts.montserrat(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.shopping_bag_outlined),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const OrdersScreen()),
              );
            },
            tooltip: isHindi ? 'मेरे ऑर्डर' : 'My Orders',
          ),
        ],
      ),
      body: productsProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : productsProvider.error.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        isHindi ? 'कुछ गलत हो गया!' : 'Something went wrong!',
                        style: GoogleFonts.montserrat(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(productsProvider.error),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => productsProvider.fetchProducts(),
                        child: Text(isHindi ? 'पुनः प्रयास करें' : 'Try Again'),
                      ),
                    ],
                  ),
                )
              : productsProvider.products.isEmpty
                  ? Center(
                      child: Text(
                        isHindi ? 'कोई उत्पाद उपलब्ध नहीं है' : 'No products available',
                        style: GoogleFonts.montserrat(fontSize: 16),
                      ),
                    )
                  : GridView.builder(
                      padding: const EdgeInsets.all(16),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.7,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                      ),
                      itemCount: productsProvider.products.length,
                      itemBuilder: (context, index) {
                        final product = productsProvider.products[index];
                        return _buildProductCard(context, product, coinsProvider.coins);
                      },
                    ),
    );
  }

  Widget _buildProductCard(BuildContext context, ProductApi product, int userCoins) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    final canAfford = userCoins >= product.coinPrice;

    return GestureDetector(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => ProductApiDetailScreen(productId: product.id),
        ),
      ),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              child: Stack(
                children: [
                  Image.network(
                    product.image,
                    height: 120,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.image_not_supported, size: 40),
                    ),
                  ),
                  if (!product.inStock)
                    Container(
                      height: 120,
                      width: double.infinity,
                      color: Colors.black.withOpacity(0.6),
                      child: Center(
                        child: Text(
                          isHindi ? 'स्टॉक में नहीं है' : 'Out of Stock',
                          style: GoogleFonts.montserrat(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            
            // Product Details
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: GoogleFonts.montserrat(
                      fontWeight: FontWeight.w500, // Changed from bold to w500
                      fontSize: 12, // Reduced from 14 to 12
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '₹${product.price}',
                    style: GoogleFonts.montserrat(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: Colors.green[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.monetization_on,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${product.coinPrice} ${isHindi ? 'सिक्के' : 'coins'}',
                        style: GoogleFonts.montserrat(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                          color: canAfford ? Colors.amber[700] : Colors.red,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (product.inStock)
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: canAfford
                            ? () => Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (_) => ProductApiDetailScreen(productId: product.id),
                                ),
                              )
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          disabledBackgroundColor: Colors.grey,
                        ),
                        child: Text(
                          isHindi ? 'खरीदें' : 'Buy',
                          style: GoogleFonts.montserrat(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    )
                  else
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 4),
                        ),
                        child: Text(
                          isHindi ? 'स्टॉक में नहीं है' : 'Out of Stock',
                          style: GoogleFonts.montserrat(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
