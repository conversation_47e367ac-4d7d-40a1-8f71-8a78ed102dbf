import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:rupify/models/order.dart';
import 'package:rupify/providers/auth_provider.dart';
import 'package:rupify/providers/orders_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/screens/auth_screen.dart';
import 'package:rupify/screens/order_details_screen.dart';
import 'package:rupify/screens/products_screen.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({Key? key}) : super(key: key);

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      // Check if user is logged in
      if (!authProvider.isLoggedIn) {
        // Redirect to login page
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (_) => const AuthScreen(fromOrders: true),
            ),
          );
        }
        return;
      }
      
      // Load orders using token
      final ordersProvider = Provider.of<OrdersProvider>(context, listen: false);
      
      try {
        await ordersProvider.fetchUserOrders(token: authProvider.token);
      } catch (e) {
        // Handle error but don't crash
        print('Error fetching orders: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading orders: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('Error in _loadOrders: $e');
      // Handle any other errors
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final ordersProvider = Provider.of<OrdersProvider>(context);
    final isHindi = settingsProvider.isHindi;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          isHindi ? 'मेरे ऑर्डर' : 'My Orders',
          style: GoogleFonts.montserrat(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadOrders,
            tooltip: isHindi ? 'रीफ्रेश' : 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ordersProvider.error.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        ordersProvider.error,
                        style: GoogleFonts.montserrat(
                          fontSize: 16,
                          color: Colors.red.shade300,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: _loadOrders,
                        icon: const Icon(Icons.refresh),
                        label: Text(
                          isHindi ? 'पुन: प्रयास करें' : 'Try Again',
                          style: GoogleFonts.montserrat(),
                        ),
                      ),
                    ],
                  ),
                )
              : ordersProvider.orders.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.shopping_bag_outlined,
                            size: 64,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            isHindi ? 'कोई ऑर्डर नहीं मिला' : 'No orders found',
                            style: GoogleFonts.montserrat(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            isHindi
                                ? 'आपने अभी तक कोई ऑर्डर नहीं दिया है'
                                : 'You haven\'t placed any orders yet',
                            style: GoogleFonts.montserrat(
                              fontSize: 14,
                              color: Colors.grey.shade500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pushReplacement(
                                context,
                                MaterialPageRoute(
                                  builder: (_) => const ProductsScreen(), // Make sure this class exists
                                ),
                              );
                            },
                            icon: const Icon(Icons.shopping_cart),
                            label: Text(
                              isHindi ? 'अभी खरीदारी करें' : 'Shop Now',
                              style: GoogleFonts.montserrat(),
                            ),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _loadOrders,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: ordersProvider.orders.length,
                        itemBuilder: (context, index) {
                          final order = ordersProvider.orders[index];
                          return _buildOrderCard(context, order, isHindi, isDarkMode);
                        },
                      ),
                    ),
    );
  }

  Widget _buildOrdersList(OrdersProvider ordersProvider, bool isHindi, bool isDarkMode) {
    return RefreshIndicator(
      onRefresh: () => ordersProvider.fetchOrders(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: ordersProvider.orders.length,
        itemBuilder: (context, index) {
          final order = ordersProvider.orders[index];
          return _buildOrderCard(context, order, isHindi, isDarkMode);
        },
      ),
    );
  }

  Widget _buildOrderCard(BuildContext context, Order order, bool isHindi, bool isDarkMode) {
    // Format date safely
    DateTime orderDate;
    try {
      orderDate = DateTime.parse(order.date);
    } catch (e) {
      orderDate = DateTime.now(); // Fallback to current date if parsing fails
    }
    final formattedDate = '${orderDate.day}/${orderDate.month}/${orderDate.year}';
    
    // Status color
    Color statusColor;
    switch ((order.status ?? 'pending').toLowerCase()) {
      case 'completed':
        statusColor = Colors.green;
        break;
      case 'processing':
        statusColor = Colors.blue;
        break;
      case 'pending':
        statusColor = Colors.orange;
        break;
      case 'cancelled':
        statusColor = Colors.red;
        break;
      default:
        statusColor = Colors.grey;
    }
    
    // Safely handle order ID - ensure it's never null or empty
    String displayId = order.id ?? 'UNKNOWN';
    if (displayId.isEmpty) {
      displayId = 'ORDER-${DateTime.now().millisecondsSinceEpoch}';
    }
    
    // Don't try to truncate if the ID is too short
    String shortId = displayId;
    if (displayId.length > 8) {
      shortId = displayId.substring(0, 8);
    }
    
    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // Only navigate if we have a valid order
          if (order.id != null && order.id.isNotEmpty) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => OrderDetailsScreen(order: order),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order ID and Date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${isHindi ? 'ऑर्डर आईडी' : 'Order ID'}: #${shortId}',
                    style: GoogleFonts.montserrat(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                  Text(
                    formattedDate,
                    style: GoogleFonts.montserrat(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              const Divider(height: 24),
              
              // Product name
              Text(
                order.productName ?? 'Unknown Product',
                style: GoogleFonts.montserrat(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              
              // Quantity and coins used
              Row(
                children: [
                  Text(
                    '${isHindi ? 'मात्रा' : 'Quantity'}: ${order.quantity}',
                    style: GoogleFonts.lato(
                      fontSize: 14,
                      color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Row(
                    children: [
                      const Icon(Icons.monetization_on, color: Colors.amber, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '${order.coinsUsed} ${isHindi ? 'सिक्के' : 'coins'}',
                        style: GoogleFonts.lato(
                          fontSize: 14,
                          color: isDarkMode ? Colors.amber.shade300 : Colors.amber.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Status
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: statusColor),
                ),
                child: Text(
                  isHindi
                      ? _getStatusInHindi(order.status ?? 'pending')
                      : (order.status ?? 'pending'),
                  style: GoogleFonts.montserrat(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getStatusInHindi(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'पूर्ण';
      case 'processing':
        return 'प्रोसेसिंग';
      case 'pending':
        return 'लंबित';
      case 'cancelled':
        return 'रद्द';
      default:
        return status;
    }
  }

  Widget _buildErrorWidget(OrdersProvider ordersProvider, bool isHindi) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          const SizedBox(height: 16),
          Text(
            isHindi ? 'कुछ गलत हो गया' : 'Something went wrong',
            style: GoogleFonts.montserrat(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            ordersProvider.error,
            textAlign: TextAlign.center,
            style: GoogleFonts.lato(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => ordersProvider.fetchOrders(),
            icon: const Icon(Icons.refresh),
            label: Text(isHindi ? 'पुनः प्रयास करें' : 'Try Again'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyOrdersWidget(bool isHindi) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.shopping_bag_outlined,
            color: Colors.grey,
            size: 60,
          ),
          const SizedBox(height: 16),
          Text(
            isHindi ? 'कोई ऑर्डर नहीं मिला' : 'No orders found',
            style: GoogleFonts.montserrat(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isHindi
                ? 'आपने अभी तक कोई उत्पाद नहीं खरीदा है'
                : 'You haven\'t purchased any products yet',
            textAlign: TextAlign.center,
            style: GoogleFonts.lato(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }
}
