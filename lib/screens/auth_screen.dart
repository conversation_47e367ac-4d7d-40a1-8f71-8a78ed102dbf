import 'dart:convert';
import 'dart:developer' as developer; // Add proper import for log
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:rupify/providers/auth_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/providers/coins_provider.dart';
import 'package:rupify/screens/home_screen.dart';

class AuthScreen extends StatefulWidget {
  final bool fromOrders;

  const AuthScreen({super.key, this.fromOrders = false});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _referralController = TextEditingController();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController(); // Add email controller

  bool _isLoading = false;
  bool _showPassword = false;
  bool _isSignUp = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    _referralController.dispose();
    _nameController.dispose();
    _emailController.dispose(); // Dispose email controller
    super.dispose();
  }

  Future<void> _handleAuth() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        
        if (_isSignUp) {
          // Add name field for signup
          if (_nameController.text.isEmpty) {
            throw Exception('Name is required');
          }

          // Handle signup
          try {
            var headers = {'Content-Type': 'application/json'};
            
            var request = http.Request(
              'POST',
              Uri.parse('https://katregeneralstores.supremenews.in/api/users.php?action=signup'),
            );
            
            // Ensure all fields are properly formatted
            request.body = json.encode({
              "name": _nameController.text.trim(),
              "email": _emailController.text.trim().isEmpty ? "" : _emailController.text.trim(),
              "phone": _phoneController.text.trim(),
              "password": _passwordController.text,
              "refer_by": _referralController.text.isEmpty ? null : _referralController.text.trim(),
            });
            
            developer.log('Request body: ${request.body}'); // Log request body for debugging
            
            request.headers.addAll(headers);
            
            http.StreamedResponse response = await request.send();
            
            // Get response as string
            String responseBody = await response.stream.bytesToString();
            developer.log('Response status: ${response.statusCode}');
            developer.log('Response body: $responseBody');
            
            if (response.statusCode == 200) {
              // Check if response is not empty
              if (responseBody.isNotEmpty) {
                // Parse JSON
                Map<String, dynamic> data = json.decode(responseBody);
                
                if (data['success'] == true) {
                  // Login the user
                  await authProvider.login(
                    data['user_id'].toString(),
                    _phoneController.text,
                    token: data['token'],
                    name: _nameController.text,
                    referralCode: data['referral_code'],
                    coins: data['coins'], // Add coins from signup response
                  );
                  
                  // Process referral if provided
                  if (_referralController.text.isNotEmpty) {
                    final coinsProvider = Provider.of<CoinsProvider>(
                      context,
                      listen: false,
                    );
                    
                    // Wait a moment to ensure the auth provider is updated
                    await Future.delayed(const Duration(milliseconds: 500));
                    
                    final referralResult = await coinsProvider.processReferral(
                      _referralController.text,
                    );
                    
                    if (mounted && referralResult['success']) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(referralResult['message']),
                          backgroundColor: Colors.green,
                        ),
                      );
                    } else if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(referralResult['message']),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                  
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Account created successfully!'),
                      ),
                    );
                    
                    if (widget.fromOrders) {
                      Navigator.pop(context);
                    } else {
                      Navigator.pop(context);
                    }
                  }
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(data['message'] ?? 'Signup failed')),
                    );
                  }
                }
              } else {
                throw Exception('Server returned empty response');
              }
            } else {
              throw Exception('Server error: ${response.reasonPhrase}');
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error: ${e.toString()}')),
              );
            }
          }
        } else {
          // Handle login using the new method
          try {
            await authProvider.loginWithCredentials(
              _phoneController.text,
              _passwordController.text,
            );
            
            // Check if login was successful
            if (!authProvider.isLoggedIn) {
              throw Exception('Login failed');
            }
            
            // Sync coins with server - wrap in try/catch to prevent crashes
            try {
              final coinsProvider = Provider.of<CoinsProvider>(
                context, 
                listen: false,
              );
              await coinsProvider.syncCoinsWithServer();
            } catch (e) {
              debugPrint('Error syncing coins: $e');
              // Continue with login flow even if coin sync fails
            }
            
            // Navigate to home screen
            if (mounted) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const HomeScreen()),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error: ${e.toString()}'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isSignUp
              ? (isHindi ? 'साइन अप करें' : 'Sign Up')
              : (isHindi ? 'लॉगिन' : 'Login'),
          style: GoogleFonts.montserrat(fontWeight: FontWeight.bold),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // App Logo or Icon
                Center(
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.account_balance_wallet,
                      size: 60,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Title
                Text(
                  _isSignUp
                      ? (isHindi ? 'अपना खाता बनाएं' : 'Create Your Account')
                      : (isHindi
                          ? 'अपने खाते में लॉगिन करें'
                          : 'Login to Your Account'),
                  style: GoogleFonts.montserrat(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 8),

                // Subtitle
                Text(
                  _isSignUp
                      ? (isHindi
                          ? 'अपना विवरण दर्ज करें'
                          : 'Enter your details to get started')
                      : (isHindi
                          ? 'अपने ऑर्डर देखने के लिए लॉगिन करें'
                          : 'Login to view your orders'),
                  style: GoogleFonts.lato(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Name Field (only for signup)
                if (_isSignUp)
                  Column(
                    children: [
                      TextFormField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          labelText: isHindi ? 'नाम' : 'Name',
                          hintText:
                              isHindi
                                  ? 'अपना नाम दर्ज करें'
                                  : 'Enter your name',
                          prefixIcon: const Icon(Icons.person),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor:
                              isDarkMode
                                  ? Colors.grey.shade800
                                  : Colors.grey.shade50,
                        ),
                        validator: (value) {
                          if (_isSignUp && (value == null || value.isEmpty)) {
                            return isHindi
                                ? 'नाम आवश्यक है'
                                : 'Name is required';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Email Field (optional for signup)
                      TextFormField(
                        controller: _emailController,
                        decoration: InputDecoration(
                          labelText: isHindi ? 'ईमेल (वैकल्पिक)' : 'Email (Optional)',
                          hintText: isHindi ? 'अपना ईमेल दर्ज करें' : 'Enter your email',
                          prefixIcon: const Icon(Icons.email),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            // Simple email validation
                            if (!value.contains('@') || !value.contains('.')) {
                              return isHindi ? 'वैध ईमेल दर्ज करें' : 'Enter a valid email';
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),

                // Phone Field
                TextFormField(
                  controller: _phoneController,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'फोन नंबर' : 'Phone Number',
                    hintText:
                        isHindi
                            ? '10 अंकों का नंबर दर्ज करें'
                            : 'Enter 10 digit number',
                    prefixIcon: const Icon(Icons.phone_android),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor:
                        isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
                  ),
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isHindi
                          ? 'फोन नंबर आवश्यक है'
                          : 'Phone number is required';
                    }
                    if (value.length != 10 ||
                        !RegExp(r'^[0-9]+$').hasMatch(value)) {
                      return isHindi
                          ? 'वैध 10 अंकों का नंबर दर्ज करें'
                          : 'Enter valid 10 digit number';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Password Field
                TextFormField(
                  controller: _passwordController,
                  obscureText: !_showPassword,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'पासवर्ड' : 'Password',
                    hintText:
                        isHindi
                            ? 'अपना पासवर्ड दर्ज करें'
                            : 'Enter your password',
                    prefixIcon: const Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _showPassword ? Icons.visibility_off : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _showPassword = !_showPassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor:
                        isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isHindi
                          ? 'पासवर्ड आवश्यक है'
                          : 'Password is required';
                    }
                    if (_isSignUp && value.length < 6) {
                      return isHindi
                          ? 'पासवर्ड कम से कम 6 अक्षर का होना चाहिए'
                          : 'Password must be at least 6 characters';
                    }
                    return null;
                  },
                ),

                // Referral Code Field (only for signup)
                if (_isSignUp) ...[
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _referralController,
                    decoration: InputDecoration(
                      labelText:
                          isHindi
                              ? 'रेफरल कोड (वैकल्पिक)'
                              : 'Referral Code (Optional)',
                      hintText:
                          isHindi
                              ? 'यदि आपके पास है तो दर्ज करें'
                              : 'Enter if you have one',
                      prefixIcon: const Icon(Icons.card_giftcard),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor:
                          isDarkMode
                              ? Colors.grey.shade800
                              : Colors.grey.shade50,
                    ),
                  ),
                ],

                const SizedBox(height: 24),

                // Login/Signup Button
                ElevatedButton(
                  onPressed: _isLoading ? null : _handleAuth,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child:
                      _isLoading
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : Text(
                            _isSignUp
                                ? (isHindi ? 'साइन अप करें' : 'Sign Up')
                                : (isHindi ? 'लॉगिन करें' : 'Login'),
                            style: GoogleFonts.montserrat(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                ),

                const SizedBox(height: 24),

                // Toggle between Login and Signup
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _isSignUp
                          ? (isHindi
                              ? 'पहले से खाता है?'
                              : 'Already have an account?')
                          : (isHindi
                              ? 'खाता नहीं है?'
                              : 'Don\'t have an account?'),
                      style: GoogleFonts.lato(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        setState(() {
                          _isSignUp = !_isSignUp;
                        });
                      },
                      child: Text(
                        _isSignUp
                            ? (isHindi ? 'लॉगिन करें' : 'Login')
                            : (isHindi ? 'साइन अप करें' : 'Sign Up'),
                        style: GoogleFonts.montserrat(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
