import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:rupify/providers/coins_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/providers/auth_provider.dart';
import 'package:flutter/services.dart';
import 'package:rupify/screens/auth_screen.dart';
import 'package:share_plus/share_plus.dart';

class CheckInScreen extends StatefulWidget {
  const CheckInScreen({super.key});

  @override
  State<CheckInScreen> createState() => _CheckInScreenState();
}

class _CheckInScreenState extends State<CheckInScreen> {
  bool _hasCheckedInToday = false;

  @override
  void initState() {
    super.initState();
    
    // Check if user has already checked in today
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkIfAlreadyCheckedIn();
      _autoCheckIn();
    });
  }

  // Check if already checked in
  void _checkIfAlreadyCheckedIn() {
    final coinsProvider = Provider.of<CoinsProvider>(context, listen: false);
    final lastCheckIn = coinsProvider.lastCheckIn;
    
    if (lastCheckIn != null) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final lastCheckInDate = DateTime(
        lastCheckIn.year, lastCheckIn.month, lastCheckIn.day);
      
      setState(() {
        _hasCheckedInToday = lastCheckInDate.isAtSameMomentAs(today);
      });
    }
  }

  // Auto check-in function
  void _autoCheckIn() async {
    if (_hasCheckedInToday) return;
    
    final coinsProvider = Provider.of<CoinsProvider>(context, listen: false);
    final result = await coinsProvider.checkIn();
    
    if (result['success'] && mounted) {
      setState(() {
        _hasCheckedInToday = true;
      });
      _showRewardDialog(context, result);
    }
  }

  @override
  Widget build(BuildContext context) {
    final coinsProvider = Provider.of<CoinsProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isHindi = settingsProvider.isHindi;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = const Color(0xFF36916D);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: primaryColor.withOpacity(0.1),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: primaryColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          isHindi ? 'दैनिक चेक-इन' : 'Daily Check-in',
          style: TextStyle(color: primaryColor),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // Check-in status message
                    if (_hasCheckedInToday)
                      Container(
                        margin: const EdgeInsets.only(bottom: 24.0),
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          color: Colors.green.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.green),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.check_circle, color: Colors.green),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                isHindi
                                    ? 'आपने आज पहले ही चेक-इन कर लिया है!'
                                    : 'You have already checked in today!',
                                style: GoogleFonts.lato(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    
                    Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Text(
                              isHindi ? 'आपके सिक्के' : 'Your Coins',
                              style: GoogleFonts.montserrat(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.monetization_on,
                                  color: Colors.amber,
                                  size: 32,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '${coinsProvider.coins}',
                                  style: GoogleFonts.montserrat(
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              isHindi
                                  ? 'लगातार चेक-इन: ${coinsProvider.consecutiveCheckIns} दिन'
                                  : 'Consecutive Check-ins: ${coinsProvider.consecutiveCheckIns} days',
                              style: GoogleFonts.lato(
                                fontSize: 16,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Check-in button
                    if (!_hasCheckedInToday)
                      ElevatedButton(
                        onPressed: () async {
                          final result = await coinsProvider.checkIn();
                          
                          if (result['success'] && mounted) {
                            setState(() {
                              _hasCheckedInToday = true;
                            });
                            _showRewardDialog(context, result);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          isHindi ? 'आज चेक-इन करें' : 'Check-in Today',
                          style: GoogleFonts.montserrat(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    
                    const SizedBox(height: 32),
                    
                    // Referral section
                    Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              isHindi ? 'दोस्तों को आमंत्रित करें' : 'Invite Friends',
                              style: GoogleFonts.montserrat(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              isHindi
                                  ? 'अपने रेफरल कोड को शेयर करें और प्रत्येक नए उपयोगकर्ता के लिए 10 सिक्के प्राप्त करें!'
                                  : 'Share your referral code and earn 10 coins for each new user!',
                              style: GoogleFonts.lato(
                                fontSize: 14,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            // Referral code display
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                color: isDarkMode 
                                    ? Colors.grey.shade800 
                                    : Colors.grey.shade100,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: isDarkMode 
                                      ? Colors.grey.shade700 
                                      : Colors.grey.shade300,
                                ),
                              ),
                              child: InkWell(
                                onTap: authProvider.isLoggedIn 
                                    ? null 
                                    : () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (_) => const AuthScreen(),
                                          ),
                                        );
                                      },
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        authProvider.isLoggedIn
                                            ? authProvider.currentUser?.referralCode??""
                                            : (isHindi ? 'लॉगिन करें' : 'Login to get code'),
                                        style: GoogleFonts.robotoMono(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          color: isDarkMode 
                                              ? Colors.white 
                                              : Colors.black87,
                                        ),
                                      ),
                                    ),
                                    if (authProvider.isLoggedIn)
                                      IconButton(
                                        icon: const Icon(Icons.copy),
                                        color: primaryColor,
                                        onPressed: () {
                                          Clipboard.setData(
                                            ClipboardData(text: authProvider.currentUser?.referralCode??""),
                                          );
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                isHindi
                                                    ? 'रेफरल कोड कॉपी किया गया!'
                                                    : 'Referral code copied!',
                                              ),
                                              backgroundColor: Colors.green,
                                              duration: const Duration(seconds: 1),
                                            ),
                                          );
                                        },
                                      ),
                                    if (!authProvider.isLoggedIn)
                                      Icon(
                                        Icons.arrow_forward,
                                        color: primaryColor,
                                      ),
                                  ],
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: 16),
                            
                            // Share button
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: authProvider.isLoggedIn
                                    ? () {
                                        final message = isHindi
                                            ? 'rupify ऐप पर मेरे साथ जुड़ें! मेरा रेफरल कोड है: ${authProvider.userId}'
                                            : 'Join me on rupify app! Use my referral code: ${authProvider.userId}';
                                        Share.share(message);
                                      }
                                    : () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (_) => const AuthScreen(),
                                          ),
                                        );
                                      },
                                icon: const Icon(Icons.share),
                                label: Text(
                                  authProvider.isLoggedIn
                                      ? (isHindi ? 'दोस्तों के साथ शेयर करें' : 'Share with Friends')
                                      : (isHindi ? 'शेयर करने के लिए लॉगिन करें' : 'Login to Share'),
                                  style: GoogleFonts.lato(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: primaryColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Rewards explanation
                    Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              isHindi ? 'चेक-इन पुरस्कार' : 'Check-in Rewards',
                              style: GoogleFonts.montserrat(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 16),
                            _buildRewardItem(
                              '1',
                              isHindi ? 'दैनिक चेक-इन' : 'Daily Check-in',
                              isHindi ? '1 सिक्का' : '1 Coin',
                              isDarkMode,
                            ),
                            _buildRewardItem(
                              '3',
                              isHindi ? '3 दिन लगातार' : '3 Days Streak',
                              isHindi ? '3 अतिरिक्त सिक्के' : '3 Extra Coins',
                              isDarkMode,
                            ),
                            _buildRewardItem(
                              '7',
                              isHindi ? '7 दिन लगातार' : '7 Days Streak',
                              isHindi ? '3 अतिरिक्त सिक्के' : '3 Extra Coins',
                              isDarkMode,
                            ),
                            _buildRewardItem(
                              '30',
                              isHindi ? '30 दिन लगातार' : '30 Days Streak',
                              isHindi ? '10 अतिरिक्त सिक्के' : '10 Extra Coins',
                              isDarkMode,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRewardItem(String days, String title, String reward, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: const Color(0xFF36916D).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                days,
                style: GoogleFonts.montserrat(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF36916D),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: GoogleFonts.lato(
                fontSize: 14,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
          ),
          Text(
            reward,
            style: GoogleFonts.lato(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.amber.shade800,
            ),
          ),
        ],
      ),
    );
  }

  void _showRewardDialog(BuildContext context, Map<String, dynamic> result) {
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          isHindi ? 'चेक-इन पूरा हुआ!' : 'Check-in Complete!',
          style: GoogleFonts.montserrat(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              result['message'] ?? (isHindi ? 'आपने आज चेक-इन किया!' : 'You checked in today!'),
              textAlign: TextAlign.center,
              style: GoogleFonts.lato(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.monetization_on,
                  color: Colors.amber,
                ),
                const SizedBox(width: 8),
                Text(
                  '+${result['coinsEarned']}',
                  style: GoogleFonts.montserrat(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              isHindi
                  ? 'कुल सिक्के: ${result['totalCoins']}'
                  : 'Total Coins: ${result['totalCoins']}',
              style: GoogleFonts.lato(
                fontSize: 16,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isHindi
                  ? 'लगातार चेक-इन: ${result['streak']} दिन'
                  : 'Streak: ${result['streak']} days',
              style: GoogleFonts.lato(
                fontSize: 16,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
            },
            child: Text(
              isHindi ? 'ठीक है' : 'OK',
              style: GoogleFonts.montserrat(
                fontWeight: FontWeight.bold,
                color: const Color(0xFF36916D),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
