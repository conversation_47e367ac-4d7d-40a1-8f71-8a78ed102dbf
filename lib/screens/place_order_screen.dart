import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:rupify/models/product_api.dart';
import 'package:rupify/models/order.dart';
import 'package:rupify/providers/auth_provider.dart';
import 'package:rupify/providers/coins_provider.dart';
import 'package:rupify/providers/orders_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/screens/order_success_screen.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:developer' as developer;

class PlaceOrderScreen extends StatefulWidget {
  final ProductApi product;

  const PlaceOrderScreen({
    super.key,
    required this.product,
  });

  @override
  State<PlaceOrderScreen> createState() => _PlaceOrderScreenState();
}

class _PlaceOrderScreenState extends State<PlaceOrderScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _alternatePhoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();
  int _quantity = 1;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Pre-fill form with user data if available
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.isLoggedIn && authProvider.currentUser != null) {
      _nameController.text = authProvider.currentUser!.name;
      _phoneController.text = authProvider.currentUser!.phone ?? '';
      _addressController.text = authProvider.currentUser!.address ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _alternatePhoneController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final coinsProvider = Provider.of<CoinsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    final totalCoins = widget.product.coinPrice * _quantity;
    final canAfford = coinsProvider.coins >= totalCoins;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isHindi ? 'ऑर्डर प्लेस करें' : 'Place Order',
          style: GoogleFonts.montserrat(fontWeight: FontWeight.bold),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product Summary
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Product Image
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            widget.product.image,
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Container(
                              width: 80,
                              height: 80,
                              color: Colors.grey[300],
                              child: const Icon(Icons.image_not_supported, size: 40),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        
                        // Product Details
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.product.name,
                                style: GoogleFonts.montserrat(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '₹${widget.product.price}',
                                style: GoogleFonts.montserrat(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.green[700],
                                ),
                              ),
                              const SizedBox(height: 8),
                              
                              // Quantity Selector
                              Row(
                                children: [
                                  Text(
                                    isHindi ? 'मात्रा:' : 'Quantity:',
                                    style: GoogleFonts.montserrat(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey.shade300),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      children: [
                                        IconButton(
                                          icon: const Icon(Icons.remove, size: 16),
                                          onPressed: _quantity > 1
                                              ? () => setState(() => _quantity--)
                                              : null,
                                          constraints: const BoxConstraints(
                                            minWidth: 32,
                                            minHeight: 32,
                                          ),
                                          padding: EdgeInsets.zero,
                                        ),
                                        Text(
                                          '$_quantity',
                                          style: GoogleFonts.montserrat(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        IconButton(
                                          icon: const Icon(Icons.add, size: 16),
                                          onPressed: () => setState(() => _quantity++),
                                          constraints: const BoxConstraints(
                                            minWidth: 32,
                                            minHeight: 32,
                                          ),
                                          padding: EdgeInsets.zero,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Total Coins
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.amber.shade200),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        isHindi ? 'कुल सिक्के:' : 'Total Coins:',
                        style: GoogleFonts.montserrat(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Row(
                        children: [
                          const Icon(Icons.monetization_on, color: Colors.amber, size: 20),
                          const SizedBox(width: 4),
                          Text(
                            '$totalCoins',
                            style: GoogleFonts.montserrat(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: canAfford ? Colors.green[700] : Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                if (!canAfford)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      isHindi ? 'आपके पास पर्याप्त सिक्के नहीं हैं!' : 'You don\'t have enough coins!',
                      style: GoogleFonts.montserrat(
                        fontSize: 14,
                        color: Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                
                const SizedBox(height: 24),
                
                // Shipping Information
                Text(
                  isHindi ? 'शिपिंग जानकारी' : 'Shipping Information',
                  style: GoogleFonts.montserrat(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Name
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'पूरा नाम' : 'Full Name',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.person),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isHindi ? 'नाम आवश्यक है' : 'Name is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // Phone
                TextFormField(
                  controller: _phoneController,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'फोन नंबर' : 'Phone Number',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.phone),
                  ),
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isHindi ? 'फोन नंबर आवश्यक है' : 'Phone number is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // Alternate Phone (Optional)
                TextFormField(
                  controller: _alternatePhoneController,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'वैकल्पिक फोन नंबर (वैकल्पिक)' : 'Alternate Phone Number (Optional)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.phone_android),
                  ),
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),
                
                // Address
                TextFormField(
                  controller: _addressController,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'पूरा पता' : 'Full Address',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.home),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isHindi ? 'पता आवश्यक है' : 'Address is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // Notes (Optional)
                TextFormField(
                  controller: _notesController,
                  decoration: InputDecoration(
                    labelText: isHindi ? 'अतिरिक्त नोट्स (वैकल्पिक)' : 'Additional Notes (Optional)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.note),
                  ),
                  maxLines: 2,
                ),
                
                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ElevatedButton(
          onPressed: canAfford ? _placeOrder : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Text(
            isHindi ? 'ऑर्डर प्लेस करें' : 'Place Order',
            style: GoogleFonts.montserrat(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  void _placeOrder() async {
    if (_formKey.currentState!.validate()) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final coinsProvider = Provider.of<CoinsProvider>(context, listen: false);
      
      // Check if user is logged in
      if (!authProvider.isLoggedIn) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please login to place an order'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
      
      setState(() {
        _isLoading = true;
      });
      
      try {
        // Ensure quantity is at least 1
        final quantity = _quantity < 1 ? 1 : _quantity;
        
        // Create order data
        final orderData = {
          'customer_name': _nameController.text,
          'phone_primary': _phoneController.text,
          'phone_alternate': _alternatePhoneController.text.isEmpty ? null : _alternatePhoneController.text,
          'address': _addressController.text,
          'notes': _notesController.text,
          'products': [
            {
              'id': widget.product.id,
              'quantity': quantity
            }
          ],
          'token': authProvider.token,
          'user_id': authProvider.userId
        };
        
        // Use OrdersProvider instead of direct API call
        final ordersProvider = Provider.of<OrdersProvider>(context, listen: false);
        
        // Create Order object
        final order = Order(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          productId: widget.product.id,
          productName: widget.product.name,
          totalAmount: double.parse(widget.product.price) * quantity,
          quantity: quantity,
          coinsUsed: widget.product.coinPrice * quantity,
          customerName: _nameController.text,
          phonePrimary: _phoneController.text,
          phoneAlternate: _alternatePhoneController.text.isEmpty ? null : _alternatePhoneController.text,
          address: _addressController.text,
          date: DateTime.now().toIso8601String(),
          status: 'pending',
        );
        
        final result = await ordersProvider.placeOrder(order, token: authProvider.token, userId: authProvider.userId);
        
        if (result['success']) {
          // Update user profile if logged in
          authProvider.updateUserProfile(
            phone: _phoneController.text,
            address: _addressController.text,
            name: _nameController.text,
          );
          
          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Order placed successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            
            // Navigate to success screen
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (_) => OrderSuccessScreen(order: order),
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(result['message'] ?? 'Failed to place order'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}
