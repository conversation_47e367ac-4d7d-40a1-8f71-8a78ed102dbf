import 'dart:io';
import 'package:csv/csv.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:rupify/models/expense.dart';
import 'package:share_plus/share_plus.dart';

class ExportService {
  Future<void> exportToCsv(List<Expense> expenses) async {
    final List<List<dynamic>> rows = [];
    
    // Add header row
    rows.add(['Date', 'Category', 'Amount (₹)', 'Description']);
    
    // Add data rows
    for (var expense in expenses) {
      rows.add([
        DateFormat('dd/MM/yyyy').format(expense.date),
        expense.category.displayName,
        expense.amount,
        expense.description,
      ]);
    }
    
    // Convert to CSV
    String csv = const ListToCsvConverter().convert(rows);
    
    // Get file path
    final directory = await getApplicationDocumentsDirectory();
    final path = '${directory.path}/rupify_expenses.csv';
    final file = File(path);
    await file.writeAsString(csv);
    
    // Share the file
    await Share.shareXFiles(
      [XFile(path)],
      subject: 'rupify Expenses',
      text: 'Here are my expenses from rupify app',
    );
  }
}