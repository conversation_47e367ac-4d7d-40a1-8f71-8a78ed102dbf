import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:firebase_messaging/firebase_messaging.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = 
      FlutterLocalNotificationsPlugin();
  
  Future<void> init() async {
    // Initialize timezone data
    tz_data.initializeTimeZones();

    // Request permissions for iOS and Android
    await flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()?.requestPermissions(
      alert: true,
      badge: true,
      sound: true,
    );

    // Initialize local notifications
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification tap
        _handleNotificationTap(response);
      },
    );

    // Create the notification channel for Android
    final AndroidFlutterLocalNotificationsPlugin? androidPlugin = 
        flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();
            
    if (androidPlugin != null) {
      await androidPlugin.requestNotificationsPermission();
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          'todo_channel',
          'Todo Reminders',
          description: 'Notifications for todo reminders',
          importance: Importance.max, // Changed to max
          enableLights: true,
          enableVibration: true,
          playSound: true, // Added playSound
          sound: RawResourceAndroidNotificationSound('notification'), // Added custom sound
          ledColor: Color(0xFF36916D),
        ),
      );
    }
    
    // Configure Firebase Messaging for background notifications
    await _configureFirebaseMessaging();
  }

  void _handleNotificationTap(NotificationResponse response) {
    // Navigate to todo screen
    // We'll use payload to store the route
    if (response.payload != null) {
      // The navigation will be handled in main.dart with a GlobalKey<NavigatorState>
    }
  }

  Future<void> _configureFirebaseMessaging() async {
    // Get FCM token
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
    
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _showNotification(
        title: message.notification?.title ?? 'Todo Reminder',
        body: message.notification?.body ?? 'You have a todo coming up!',
      );
    });
  }

  Future<void> scheduleTodoNotification(
    String id,
    String title,
    String body,
    DateTime scheduledDate,
    TimeOfDay scheduledTime,
  ) async {
    print('Scheduling notification for todo: $title'); // Debug log

    // Calculate the todo time
    final DateTime todoDateTime = DateTime(
      scheduledDate.year,
      scheduledDate.month,
      scheduledDate.day,
      scheduledTime.hour,
      scheduledTime.minute,
    );
    
    // Create notification details with sound and high priority
    final NotificationDetails notificationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        'todo_channel',
        'Todo Reminders',
        channelDescription: 'Notifications for todo reminders',
        importance: Importance.max,
        priority: Priority.max,
        icon: '@mipmap/ic_launcher',
        largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        color: const Color(0xFF36916D),
        enableLights: true,
        playSound: true,
        sound: const RawResourceAndroidNotificationSound('notification'),
        ledColor: const Color(0xFF36916D),
        ledOnMs: 1000,
        ledOffMs: 500,
        styleInformation: BigTextStyleInformation(
          body,
          htmlFormatBigText: true,
          contentTitle: title,
          htmlFormatContentTitle: true,
          summaryText: 'Todo Reminder',
          htmlFormatSummaryText: true,
        ),
      ),
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
        badgeNumber: 1,
      ),
    );
    
    try {
      // Always show an immediate confirmation notification
      final formattedTime = '${scheduledTime.hour.toString().padLeft(2, '0')}:${scheduledTime.minute.toString().padLeft(2, '0')}';
      await flutterLocalNotificationsPlugin.show(
        int.parse(id) * 100,
        'Todo Scheduled',
        'Task "$title" scheduled for $formattedTime',
        notificationDetails,
      );
      print('Scheduled confirmation notification sent'); // Debug log
    } catch (e) {
      print('Error showing task scheduled notification: $e');
    }
    
    // If the todo is scheduled within 5 minutes or in the past, show an immediate notification
    if (todoDateTime.difference(DateTime.now()).inMinutes <= 5) {
      try {
        await flutterLocalNotificationsPlugin.show(
          int.parse(id),
          title,
          'This todo is due ${todoDateTime.isBefore(DateTime.now()) ? "now" : "very soon"}!',
          notificationDetails,
        );
        print('Immediate notification sent for near-future todo'); // Debug log
        return; // Exit early after showing immediate notification
      } catch (e) {
        print('Error showing immediate notification: $e');
      }
    }
    
    // Schedule future notifications
    if (todoDateTime.isAfter(DateTime.now())) {
      try {
        final scheduledTime = tz.TZDateTime.from(todoDateTime, tz.local);
        await flutterLocalNotificationsPlugin.zonedSchedule(
          int.parse(id),
          title,
          body,
          scheduledTime,
          notificationDetails,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        );
        print('Scheduled future notification for: ${scheduledTime.toString()}'); // Debug log
      } catch (e) {
        print('Error scheduling future notification: $e');
      }
    }
  }

  Future<void> cancelNotification(String id) async {
    // Cancel all notifications (scheduled confirmation, 10-min, 5-min, exact time, and passed)
    final int scheduleConfirmId = int.parse(id) * 100;
    final int tenMinId = int.parse(id) * 10;
    final int fiveMinId = int.parse(id) * 5;
    final int exactTimeId = int.parse(id);
    final int passedId = int.parse(id) * 50;
    
    await flutterLocalNotificationsPlugin.cancel(scheduleConfirmId);
    await flutterLocalNotificationsPlugin.cancel(tenMinId);
    await flutterLocalNotificationsPlugin.cancel(fiveMinId);
    await flutterLocalNotificationsPlugin.cancel(exactTimeId);
    await flutterLocalNotificationsPlugin.cancel(passedId);
  }

  Future<void> _showNotification({
    required String title,
    required String body,
  }) async {
    final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'todo_channel',
      'Todo Reminders',
      channelDescription: 'Notifications for todo reminders',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
      color: const Color(0xFF36916D),
      enableLights: true,
      ledColor: const Color(0xFF36916D),
      ledOnMs: 1000,
      ledOffMs: 500,
      styleInformation: BigTextStyleInformation(
        body,
        htmlFormatBigText: true,
        contentTitle: title,
        htmlFormatContentTitle: true,
      ),
    );
    
    final NotificationDetails platformDetails = NotificationDetails(
      android: androidDetails,
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
        badgeNumber: 1,
      ),
    );
    
    await flutterLocalNotificationsPlugin.show(
      0,
      title,
      body,
      platformDetails,
      payload: '/todo',
    );
  }
}

// Handle background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Need to ensure Firebase is initialized here
  // await Firebase.initializeApp();
  
  // No need to show a notification as the system will do it automatically
}
