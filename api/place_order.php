<?php
error_reporting(E_ALL);
ini_set('display_errors', '1');

include '../admin/connect.php';
include '../functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Only POST method is allowed']);
    exit;
}

$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    $data = $_POST;
}

// Check for authentication
$user_id = null;
$token = isset($data['token']) ? mysqli_real_escape_string($con, $data['token']) : null;
$provided_user_id = isset($data['user_id']) ? mysqli_real_escape_string($con, $data['user_id']) : null;

// Verify user exists using token or user_id
if ($token) {
    $user_query = mysqli_query($con, "SELECT id, name, phone, address FROM users WHERE token = '$token'");
    if (mysqli_num_rows($user_query) > 0) {
        $user = mysqli_fetch_assoc($user_query);
        $user_id = $user['id'];
    }
} elseif ($provided_user_id) {
    $user_query = mysqli_query($con, "SELECT id, name, phone, address FROM users WHERE id = '$provided_user_id'");
    if (mysqli_num_rows($user_query) > 0) {
        $user = mysqli_fetch_assoc($user_query);
        $user_id = $user['id'];
    }
}

// Require authentication
if (!$user_id) {
    echo json_encode(['success' => false, 'message' => 'Authentication required. Please login first.']);
    exit;
}

$required_fields = ['customer_name', 'phone_primary', 'address', 'products'];
$missing_fields = [];

foreach ($required_fields as $field) {
    if (empty($data[$field])) {
        $missing_fields[] = $field;
    }
}

if (!empty($missing_fields)) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields: ' . implode(', ', $missing_fields)]);
    exit;
}

$customer_name = mysqli_real_escape_string($con, $data['customer_name']);
$phone_primary = mysqli_real_escape_string($con, $data['phone_primary']);
$phone_alternate = isset($data['phone_alternate']) ? mysqli_real_escape_string($con, $data['phone_alternate']) : '';
$address = mysqli_real_escape_string($con, $data['address']);
$platform = isset($data['platform']) ? mysqli_real_escape_string($con, $data['platform']) : 'Mobile App';
$notes = isset($data['notes']) ? mysqli_real_escape_string($con, $data['notes']) : '';
$products = $data['products'];

if (!is_array($products) || empty($products)) {
    echo json_encode(['success' => false, 'message' => 'Products must be a non-empty array']);
    exit;
}

// Validate products array
foreach ($products as $index => $product) {
    if (!isset($product['id']) || !isset($product['quantity'])) {
        echo json_encode(['success' => false, 'message' => "Product at index $index is missing id or quantity"]);
        exit;
    }
    
    // Ensure product ID is a positive integer
    if (!is_numeric($product['id']) || intval($product['id']) <= 0) {
        echo json_encode(['success' => false, 'message' => "Product ID must be a positive number at index $index"]);
        exit;
    }
    
    // Ensure quantity is a positive integer
    if (!is_numeric($product['quantity']) || intval($product['quantity']) <= 0) {
        echo json_encode(['success' => false, 'message' => "Product quantity must be a positive number at index $index"]);
        exit;
    }
}

// Update user information if needed
$update_user = "UPDATE users SET 
                name = '$customer_name', 
                phone = '$phone_primary', 
                address = '$address', 
                updated_at = NOW() 
                WHERE id = '$user_id'";
mysqli_query($con, $update_user);

$order_number = 'ORD-' . date('Ymd') . '-' . rand(1000, 9999);
$total_amount = 0;

$valid_products = [];

foreach ($products as $product) {
    $product_id = (int)$product['id'];
    $quantity = (int)$product['quantity'];
    
    // Double-check that values are positive integers
    if ($product_id <= 0 || $quantity <= 0) {
        continue;
    }

    $product_query = mysqli_query($con, "SELECT id, title, price, offer_price, coin_price FROM products WHERE id = '$product_id'");
    if (mysqli_num_rows($product_query) > 0) {
        $product_data = mysqli_fetch_assoc($product_query);
        $price = (!empty($product_data['offer_price']) && $product_data['offer_price'] < $product_data['price']) 
                ? $product_data['offer_price'] : $product_data['price'];

        $total_amount += $price * $quantity;
        $valid_products[] = [
            'product_id' => $product_id,
            'product_name' => $product_data['title'],
            'quantity' => $quantity,
            'price' => $price,
            'coin_price' => $product_data['coin_price'] ?? 0
        ];
    }
}

if (empty($valid_products)) {
    echo json_encode(['success' => false, 'message' => 'No valid products found']);
    exit;
}

// Calculate total coins used
$total_coins_used = 0;
foreach ($valid_products as $product) {
    $total_coins_used += ($product['coin_price'] * $product['quantity']);
}

// Check if user has enough coins
$coins_query = mysqli_query($con, "SELECT coins FROM users WHERE id = '$user_id'");
$user_coins = 0;
if (mysqli_num_rows($coins_query) > 0) {
    $coins_data = mysqli_fetch_assoc($coins_query);
    $user_coins = (int)$coins_data['coins'];
}

if ($user_coins < $total_coins_used) {
    echo json_encode(['success' => false, 'message' => 'Not enough coins to place this order']);
    exit;
}

// ➤ STEP 2: Insert into customer_orders with user_id
$token = bin2hex(random_bytes(16));
$insert_order = "INSERT INTO customer_orders (
    user_id, order_number, customer_name, phone_primary, phone_alternate, 
    address, platform, order_status, notes, total_amount, coins_used, token, created_at, updated_at
) VALUES (
    '$user_id', '$order_number', '$customer_name', '$phone_primary', '$phone_alternate', 
    '$address', '$platform', 'pending', '$notes', '$total_amount', '$total_coins_used', '$token', NOW(), NOW()
)";

if (mysqli_query($con, $insert_order)) {
    $order_id = mysqli_insert_id($con);

    foreach ($valid_products as $product) {
        mysqli_query($con, "INSERT INTO order_items (order_id, product_id, quantity, price) 
            VALUES ('$order_id', '{$product['product_id']}', '{$product['quantity']}', '{$product['price']}')");
    }

    // Deduct coins from user
    if ($total_coins_used > 0) {
        $new_coins = $user_coins - $total_coins_used;
        mysqli_query($con, "UPDATE users SET coins = '$new_coins' WHERE id = '$user_id'");
        
        // Log coin transaction
        mysqli_query($con, "INSERT INTO coin_transactions (user_id, amount, balance, reason, created_at) 
                           VALUES ('$user_id', '-$total_coins_used', '$new_coins', 'Order #$order_number', NOW())");
    }

    echo json_encode([
        'success' => true,
        'message' => 'Order placed successfully',
        'order' => [
            'id' => $order_id,
            'order_number' => $order_number,
            'total_amount' => $total_amount,
            'coins_used' => $total_coins_used,
            'token' => $token,
            'user_id' => $user_id,
            'remaining_coins' => $new_coins
        ]
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'Error creating order: ' . mysqli_error($con)]);
}
