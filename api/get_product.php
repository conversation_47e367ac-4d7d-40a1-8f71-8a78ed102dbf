<?php
// Show errors for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', '1');

include '../admin/connect.php';
include '../functions.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Get product ID or slug
$product_id = isset($_GET['id']) ? $_GET['id'] : null;
$product_slug = isset($_GET['slug']) ? $_GET['slug'] : null;

if (!$product_id && !$product_slug) {
    echo json_encode([
        'success' => false,
        'message' => 'Product ID or slug is required'
    ]);
    exit;
}

// Get website URL from settings
$website_url = get_setting($pdo, 'website_url');

// Build query based on provided parameter
if ($product_id) {
    $product_id = mysqli_real_escape_string($con, $product_id);
    $sql = "SELECT p.*, c.title as category_name, c.slug as category_slug 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.id = '$product_id'";
} else {
    $product_slug = mysqli_real_escape_string($con, $product_slug);
    $sql = "SELECT p.*, c.title as category_name, c.slug as category_slug 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.slug = '$product_slug'";
}

$result = mysqli_query($con, $sql);

if (mysqli_num_rows($result) > 0) {
    $product = mysqli_fetch_assoc($result);
    $product_id = $product['id'];
    
    // Get product image with full URL
    $image_path = $product['image'] ? $website_url . "uploads/products/" . $product['image'] : 'assets/images/no.svg';
    
    // Format price
    $price = !empty($product['offer_price']) && $product['offer_price'] < $product['price'] ? $product['offer_price'] : $product['price'];
    
    // Get additional images
    $additional_images = [];
    $images_query = mysqli_query($con, "SELECT image_path FROM product_images WHERE product_id = '$product_id'");
    while ($img = mysqli_fetch_assoc($images_query)) {
        $additional_images[] = $website_url . "uploads/products/" . $img['image_path'];
    }
    
    // Get platform URLs
    $platform_urls = [];
    $platforms_query = mysqli_query($con, "SELECT * FROM product_platforms WHERE product_id = '$product_id'");
    while ($platform = mysqli_fetch_assoc($platforms_query)) {
        $platform_urls[] = [
            'platform_name' => $platform['platform_name'],
            'url' => $platform['url']
        ];
    }
    
    // Get reviews
    $reviews = [];
    $reviews_query = mysqli_query($con, "SELECT * FROM product_reviews WHERE product_id = '$product_id'");
    while ($review = mysqli_fetch_assoc($reviews_query)) {
        $review_image = $review['review_image'] ? $website_url . "uploads/products/" . $review['review_image'] : null;
        $reviews[] = [
            'source' => $review['source'],
            'review_text' => $review['review_text'],
            'rating' => $review['rating'],
            'review_url' => $review['review_url'],
            'review_image' => $review_image
        ];
    }
    
    $product_data = [
        'id' => $product['id'],
        'name' => $product['name'],
        'slug' => $product['slug'],
        'description' => $product['description'],
        'price' => $price,
        'coin_price' => $product['coin_price'],
        'original_price' => $product['price'],
        'in_stock' => (bool)$product['in_stock'],
        'is_featured' => (bool)$product['is_featured'],
        'image' => $image_path,
        'additional_images' => $additional_images,
        'category' => $product['category_name'],
        'category_slug' => $product['category_slug'],
        'platform_urls' => $platform_urls,
        'reviews' => $reviews
    ];
    
    echo json_encode([
        'success' => true,
        'product' => $product_data
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Product not found'
    ]);
}