<?php
// Show errors during development (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

include '../admin/connect.php';
include '../functions.php';

header('Content-Type: application/json');

// Allow only GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'success' => false,
        'message' => 'Only GET method is allowed'
    ]);
    exit;
}

// Fetch orders
$order_query = "SELECT * FROM customer_orders ORDER BY created_at DESC";
$order_result = mysqli_query($con, $order_query);

$orders = [];

while ($order = mysqli_fetch_assoc($order_result)) {
    $order_id = $order['id'];

    // Fetch related products (order items)
    $items_query = "SELECT oi.*, p.title AS product_name 
                    FROM order_items oi 
                    LEFT JOIN products p ON oi.product_id = p.id 
                    WHERE oi.order_id = '$order_id'";
    
    $items_result = mysqli_query($con, $items_query);
    $items = [];

    while ($item = mysqli_fetch_assoc($items_result)) {
        $items[] = [
            'product_id' => $item['product_id'],
            'product_name' => $item['product_name'],
            'quantity' => (int)$item['quantity'],
            'price' => (float)$item['price'],
        ];
    }

    // Push full order
    $orders[] = [
        'id' => $order['id'],
        'order_number' => $order['order_number'],
        'customer_name' => $order['customer_name'],
        'phone_primary' => $order['phone_primary'],
        'address' => $order['address'],
        'platform' => $order['platform'],
        'order_status' => $order['order_status'],
        'notes' => $order['notes'],
        'total_amount' => (float)$order['total_amount'],
        'created_at' => $order['created_at'],
        'items' => $items,
    ];
}

// Return JSON
echo json_encode([
    'success' => true,
    'orders' => $orders
]);
