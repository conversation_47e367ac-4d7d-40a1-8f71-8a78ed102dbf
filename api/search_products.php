<?php
// Show errors for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', '1');

include '../admin/connect.php';
include '../functions.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Get website URL from settings
$website_url = get_setting($pdo, 'website_url');

// Get the search query
$query = isset($_GET['query']) ? $_GET['query'] : '';

// Validate and sanitize input
$query = mysqli_real_escape_string($con, $query);

if (empty($query)) {
    echo json_encode([
        'success' => false,
        'message' => 'No search query provided',
        'products' => []
    ]);
    exit;
}

// Search in products only (not categories)
$sql = "SELECT p.*, c.title as category_name, c.slug as category_slug 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.name LIKE '%$query%' OR p.description LIKE '%$query%'
        ORDER BY p.is_featured DESC, p.id DESC
        LIMIT 20";

$result = mysqli_query($con, $sql);

$products = [];

if (mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
        // Get product image with full URL
        $image_path = $row['image'] ? $website_url . "uploads/products/" . $row['image'] : 'assets/images/no.svg';
        
        // Format price
        $price = !empty($row['offer_price']) && $row['offer_price'] < $row['price'] ? $row['offer_price'] : $row['price'];
        
        // Get additional images
        $additional_images = [];
        $images_query = mysqli_query($con, "SELECT image_path FROM product_images WHERE product_id = '{$row['id']}'");
        while ($img = mysqli_fetch_assoc($images_query)) {
            $additional_images[] = $website_url . "uploads/products/" . $img['image_path'];
        }
        
        $products[] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'slug' => $row['slug'],
            'description' => $row['description'],
            'price' => $price,
            'original_price' => $row['price'],
            'coin_price' => $row['coin_price'],
            'in_stock' => (bool)$row['in_stock'],
            'is_featured' => (bool)$row['is_featured'],
            'image' => $image_path,
            'additional_images' => $additional_images,
            'category' => $row['category_name'],
            'category_slug' => $row['category_slug']
        ];
    }
}

// Return JSON response
echo json_encode([
    'success' => true,
    'count' => count($products),
    'products' => $products
]);
