<?php

include '../admin/connect.php';

include '../functions.php';

// ======= HEADERS & JSON =========
header("Content-Type: application/json");
$requestMethod = $_SERVER["REQUEST_METHOD"];
$action = $_GET['action'] ?? '';

// =========== SCHEMA (commented) ==============
// CREATE TABLE users (
//     id INT AUTO_INCREMENT PRIMARY KEY,
//     name VARCHAR(100) NOT NULL,
//     phone VARCHAR(20) UNIQUE DEFAULT NULL,
//     email VARCHAR(100) UNIQUE DEFAULT NULL,
//     password VARCHAR(255) DEFAULT NULL,
//     refer_by VARCHAR(100) DEFAULT NULL,
//     referral_code VARCHAR(100) UNIQUE DEFAULT NULL,
//     token VARCHAR(255) DEFAULT NULL,
//     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
// );



// =========== UTILITIES ==============
function randomString($length = 8)
{
    return strtoupper(bin2hex(random_bytes($length / 2)));
}

function generateToken($length = 60)
{
    return bin2hex(random_bytes($length / 2));
}

function jsonResponse($data, $code = 200)
{
    http_response_code($code);
    echo json_encode($data);
    exit;
}

// ========== SIGNUP ==========
// POST /users.php?action=signup
// Required: name, email/phone (at least one), password (optional), refer_by (optional)
if ($action === 'signup' && $requestMethod === 'POST') {
    // Log the raw request for debugging
    error_log("Signup request: " . file_get_contents("php://input"));

    $data = json_decode(file_get_contents("php://input"), true);

    // Check if JSON is valid
    if (json_last_error() !== JSON_ERROR_NONE) {
        jsonResponse(['success' => false, 'message' => 'Invalid JSON: ' . json_last_error_msg()], 400);
    }

    // Validate required fields
    $name = isset($data['name']) ? trim($data['name']) : '';
    $email = isset($data['email']) && !empty($data['email']) ? trim($data['email']) : null;
    $phone = isset($data['phone']) ? trim($data['phone']) : null;
    $password = isset($data['password']) ? $data['password'] : null;
    $refer_by = isset($data['refer_by']) ? trim($data['refer_by']) : null;

    // Validate required fields
    if (empty($name)) {
        jsonResponse(['success' => false, 'message' => 'Name is required'], 400);
    }

    if (empty($phone)) {
        jsonResponse(['success' => false, 'message' => 'Phone number is required'], 400);
    }

    // Validate phone number format (10 digits)
    if (!preg_match('/^\d{10}$/', $phone)) {
        jsonResponse(['success' => false, 'message' => 'Phone number must be 10 digits'], 400);
    }

    // If email is provided, validate it
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['success' => false, 'message' => 'Invalid email format'], 400);
    }

    // If password is empty, set a default one
    if (empty($password)) {
        $password = substr(md5(rand()), 0, 8); // Generate a random password
    }

    $referral_code = randomString(8);
    $token = generateToken();
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Get initial coins from settings
    $initial_coins = 10; // Default value
    try {
        $settings_stmt = $pdo->prepare("SELECT value FROM settings WHERE name = 'initial_coins'");
        $settings_stmt->execute();
        $setting = $settings_stmt->fetch(PDO::FETCH_ASSOC);
        if ($setting) {
            $initial_coins = (int)$setting['value'];
        }
    } catch (PDOException $e) {
        error_log("Error getting initial coins: " . $e->getMessage());
        // Use default if settings query fails
    }

    try {
        // Check if the user already exists
        $check_stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ?");
        $check_stmt->execute([$phone]);
        $existing_user = $check_stmt->fetch(PDO::FETCH_ASSOC);

        if ($existing_user) {
            jsonResponse(['success' => false, 'message' => 'User with this phone number already exists'], 400);
        }

        // If email is provided, check if it's already in use
        if (!empty($email)) {
            $check_email_stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $check_email_stmt->execute([$email]);
            $existing_email = $check_email_stmt->fetch(PDO::FETCH_ASSOC);

            if ($existing_email) {
                jsonResponse(['success' => false, 'message' => 'Email is already in use'], 400);
            }
        }

        // Check if coins column exists in users table
        try {
            $column_check = $pdo->query("SHOW COLUMNS FROM users LIKE 'coins'");
            $coins_column_exists = $column_check->rowCount() > 0;

            if (!$coins_column_exists) {
                // Add coins column if it doesn't exist
                $pdo->exec("ALTER TABLE users ADD COLUMN coins INT DEFAULT 0");
                error_log("Added coins column to users table");
            }
        } catch (PDOException $e) {
            error_log("Error checking/adding coins column: " . $e->getMessage());
            // Continue anyway
        }

        // Insert the new user - handle both with and without coins column
        try {
            $stmt = $pdo->prepare("INSERT INTO users (name, email, phone, password, refer_by, referral_code, token, coins) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$name, $email, $phone, $hashed_password, $refer_by, $referral_code, $token, $initial_coins]);
        } catch (PDOException $e) {
            // If the first query fails, try without the coins column
            if (strpos($e->getMessage(), 'Unknown column') !== false) {
                error_log("Trying insert without coins column: " . $e->getMessage());
                $stmt = $pdo->prepare("INSERT INTO users (name, email, phone, password, refer_by, referral_code, token) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$name, $email, $phone, $hashed_password, $refer_by, $referral_code, $token]);

                // Now try to update with coins
                try {
                    $update_stmt = $pdo->prepare("UPDATE users SET coins = ? WHERE token = ?");
                    $update_stmt->execute([$initial_coins, $token]);
                } catch (PDOException $e2) {
                    error_log("Error updating coins: " . $e2->getMessage());
                    // Continue anyway
                }
            } else {
                // If it's not a column issue, rethrow the exception
                throw $e;
            }
        }

        $user_id = $pdo->lastInsertId();

        // Log the initial coins transaction
        try {
            // Check if coin_transactions table exists
            $table_check = $pdo->query("SHOW TABLES LIKE 'coin_transactions'");
            if ($table_check->rowCount() == 0) {
                // Create coin_transactions table if it doesn't exist
                $pdo->exec("CREATE TABLE IF NOT EXISTS coin_transactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    amount INT NOT NULL,
                    balance INT NOT NULL,
                    reason VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )");
                error_log("Created coin_transactions table");
            }

            $log_stmt = $pdo->prepare("INSERT INTO coin_transactions (user_id, amount, balance, reason, created_at) VALUES (?, ?, ?, ?, NOW())");
            $log_stmt->execute([$user_id, $initial_coins, $initial_coins, 'Initial signup bonus']);
        } catch (PDOException $e) {
            error_log("Error logging coin transaction: " . $e->getMessage());
            // Ignore transaction logging errors
        }

        jsonResponse([
            'success' => true,
            'message' => 'Account created successfully',
            'user_id' => $user_id,
            'token' => $token,
            'referral_code' => $referral_code,
            'coins' => $initial_coins
        ]);
    } catch (PDOException $e) {
        error_log("Error in signup: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}

// ========== LOGIN ==========
// POST /users.php?action=login
// Required: identifier (email or phone), password (optional)
if ($action === 'login' && $requestMethod === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);
    $identifier = $data['identifier'] ?? '';
    $password = $data['password'] ?? null;

    try {
        // Use PDO connection instead of mysqli
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? OR phone = ?");
        $stmt->execute([$identifier, $identifier]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            jsonResponse(['success' => false, 'message' => 'User not found'], 404);
        }

        if ($user['password'] && $password && !password_verify($password, $user['password'])) {
            jsonResponse(['success' => false, 'message' => 'Incorrect password'], 401);
        }

        // Update token
        $new_token = generateToken();
        $update_stmt = $pdo->prepare("UPDATE users SET token = ? WHERE id = ?");
        $update_stmt->execute([$new_token, $user['id']]);

        jsonResponse([
            'success' => true,
            'token' => $new_token,
            'user_id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'phone' => $user['phone'],
            'referral_code' => $user['referral_code'],
            'coins' => (int)($user['coins'] ?? 0)
        ]);
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}

// ========== MY ORDERS ==========
// GET /users.php?action=my-orders
// Required: Authorization header with Bearer token
if ($action === 'my-orders' && $requestMethod === 'GET') {
    // Basic error logging to help diagnose issues
    error_log("Starting my-orders endpoint");

    try {
        // Get token from Authorization header
        $headers = getallheaders();
        $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

        if (empty($auth_header) || !preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            jsonResponse(['success' => false, 'message' => 'Authorization header missing or invalid'], 401);
        }

        $token = $matches[1];
        error_log("Token received: " . substr($token, 0, 10) . "...");

        // Get user by token - using mysqli instead of PDO for consistency
        $token_safe = mysqli_real_escape_string($con, $token);
        $user_query = "SELECT id FROM users WHERE token = '$token_safe' LIMIT 1";
        $user_result = mysqli_query($con, $user_query);

        if (!$user_result || mysqli_num_rows($user_result) == 0) {
            error_log("Invalid token or user not found");
            jsonResponse(['success' => false, 'message' => 'Invalid token'], 401);
        }

        $user = mysqli_fetch_assoc($user_result);
        $user_id = $user['id'];
        error_log("User ID found: $user_id");

        // Fetch orders using mysqli
        $order_query = "SELECT * FROM customer_orders WHERE user_id = '$user_id' ORDER BY created_at DESC";
        $order_result = mysqli_query($con, $order_query);

        if (!$order_result) {
            error_log("Error in order query: " . mysqli_error($con));
            jsonResponse(['success' => false, 'message' => 'Database error fetching orders'], 500);
        }

        $orders = [];

        while ($order = mysqli_fetch_assoc($order_result)) {
            $order_id = $order['id'];
            error_log("Processing order ID: $order_id");

            // Simplified order data - avoid complex joins for now
            $orders[] = [
                'id' => $order['id'],
                'order_number' => $order['order_number'] ?? '',
                'customer_name' => $order['customer_name'] ?? '',
                'phone_primary' => $order['phone_primary'] ?? '',
                'phone_alternate' => $order['phone_alternate'] ?? '',
                'address' => $order['address'] ?? '',
                'total_amount' => (float)($order['total_amount'] ?? 0),
                'coins_used' => (int)($order['coins_used'] ?? 0),
                'date' => $order['created_at'] ?? date('Y-m-d H:i:s'),
                'status' => $order['order_status'] ?? 'pending',
                // Add placeholder values for required fields
                'product_id' => 'placeholder',
                'product_name' => 'Order #' . $order['order_number'],
                'quantity' => 1
            ];
        }

        error_log("Returning " . count($orders) . " orders");

        // Return JSON
        jsonResponse([
            'success' => true,
            'orders' => $orders
        ]);
    } catch (Exception $e) {
        error_log("Critical error in my-orders: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Server error: ' . $e->getMessage()], 500);
    }
}

// ========== UPDATE COINS ==========
// POST /users.php?action=update-coins
// Required: user_id or token, coins (amount to add/subtract)
if ($action === 'update-coins' && $requestMethod === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);
    $user_id = $data['user_id'] ?? null;
    $token = $data['token'] ?? null;
    $coins = $data['coins'] ?? 0; // Can be positive (add) or negative (subtract)
    $reason = $data['reason'] ?? 'Manual update';

    if (!$user_id && !$token) {
        jsonResponse(['success' => false, 'message' => 'User ID or token required'], 400);
    }

    try {
        // Find user by ID or token
        if ($user_id) {
            $stmt = $pdo->prepare("SELECT id, name, coins FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
        } else {
            $stmt = $pdo->prepare("SELECT id, name, coins FROM users WHERE token = ?");
            $stmt->execute([$token]);
        }

        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            jsonResponse(['success' => false, 'message' => 'User not found'], 404);
        }

        // Calculate new coins balance
        $current_coins = $user['coins'] ?? 0;
        $new_coins = $current_coins + $coins;

        // Ensure coins don't go negative
        if ($new_coins < 0) {
            $new_coins = 0;
        }

        // Update user's coins
        $update_stmt = $pdo->prepare("UPDATE users SET coins = ? WHERE id = ?");
        $update_stmt->execute([$new_coins, $user['id']]);

        // Log the transaction
        $log_stmt = $pdo->prepare("INSERT INTO coin_transactions (user_id, amount, balance, reason, created_at) VALUES (?, ?, ?, ?, NOW())");
        $log_stmt->execute([$user['id'], $coins, $new_coins, $reason]);

        jsonResponse([
            'success' => true,
            'message' => 'Coins updated successfully',
            'user_id' => $user['id'],
            'previous_balance' => (int)$current_coins,
            'coins_changed' => (int)$coins,
            'new_balance' => (int)$new_coins
        ]);
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
    }
}

// ========== GET USER ==========
// GET /users.php?action=get-user
// Header: Authorization: Bearer {token}
if ($action === 'get-user') {
    if ($requestMethod !== 'GET') {
        jsonResponse([
            'success' => false,
            'message' => 'Only GET method is allowed'
        ], 405);
    }

    // Auth token validation
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    $token = str_replace('Bearer ', '', $authHeader);

    if (!$token) {
        jsonResponse(['success' => false, 'message' => 'Token missing'], 401);
    }

    // Get user by token
    $stmt = $pdo->prepare("SELECT id, name, email, phone, referral_code, coins, refer_by, created_at FROM users WHERE token = ?");
    $stmt->execute([$token]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        jsonResponse(['success' => false, 'message' => 'Invalid token'], 403);
    }

    // Get coin transaction history
    $transactions_stmt = $pdo->prepare("SELECT amount, balance, reason, created_at FROM coin_transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 20");
    $transactions_stmt->execute([$user['id']]);
    $transactions = $transactions_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format user data
    $user_data = [
        'id' => $user['id'],
        'name' => $user['name'],
        'email' => $user['email'],
        'phone' => $user['phone'],
        'referral_code' => $user['referral_code'],
        'coins' => (int)$user['coins'],
        'refer_by' => $user['refer_by'],
        'created_at' => $user['created_at'],
        'coin_transactions' => $transactions
    ];

    jsonResponse([
        'success' => true,
        'user' => $user_data
    ]);
}

// ========== GET ORDER PRODUCT DETAILS ==========
// GET /users.php?action=order-product-details
// Required: Authorization header with Bearer token, order_id
if ($action === 'order-product-details' && $requestMethod === 'GET') {
    // Get token from Authorization header
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

    if (empty($auth_header) || !preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
        jsonResponse(['success' => false, 'message' => 'Authorization header missing or invalid'], 401);
    }

    $token = $matches[1];
    $order_id = isset($_GET['order_id']) ? $_GET['order_id'] : '';

    if (empty($order_id)) {
        jsonResponse(['success' => false, 'message' => 'Order ID is required'], 400);
    }

    try {
        // Get user by token
        $stmt = $pdo->prepare("SELECT id FROM users WHERE token = ?");
        $stmt->execute([$token]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            jsonResponse(['success' => false, 'message' => 'Invalid token'], 401);
        }

        $user_id = $user['id'];

        // Verify the order belongs to the user
        $order_stmt = $pdo->prepare("SELECT id FROM customer_orders WHERE id = ? AND user_id = ?");
        $order_stmt->execute([$order_id, $user_id]);

        if ($order_stmt->rowCount() == 0) {
            jsonResponse(['success' => false, 'message' => 'Order not found or does not belong to this user'], 404);
        }

        // Get product details from order_items and products tables
        $product_stmt = $pdo->prepare("
            SELECT p.id, p.name, p.description, p.image, p.slug, p.price, p.coin_price, oi.quantity
            FROM order_items oi 
            JOIN products p ON oi.product_id = p.id 
            WHERE oi.order_id = ?
        ");
        $product_stmt->execute([$order_id]);

        if ($product_stmt->rowCount() > 0) {
            $products = [];

            // Get website URL from settings
            $website_url = get_setting($pdo, 'website_url');

            while ($product = $product_stmt->fetch(PDO::FETCH_ASSOC)) {
                // Format image URL if needed
                $image_path = $product['image'] ? $website_url . "uploads/products/" . $product['image'] : null;

                $products[] = [
                    'id' => $product['id'],
                    'name' => $product['name'],
                    'description' => $product['description'],
                    'image' => $image_path,
                    'slug' => $product['slug'],
                    'price' => (float)$product['price'],
                    'coin_price' => (int)$product['coin_price'],
                    'quantity' => (int)$product['quantity']
                ];
            }

            jsonResponse([
                'success' => true,
                'products' => $products
            ]);
        } else {
            jsonResponse(['success' => false, 'message' => 'Product details not found'], 404);
        }
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}

// ========== UPLOAD EXPENSES ==========
// POST /users.php?action=upload-expenses
// Required: Authorization header with Bearer token, expense data
if ($action === 'upload-expenses' && $requestMethod === 'POST') {
    // Get token from Authorization header
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

    if (empty($auth_header) || !preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
        jsonResponse(['success' => false, 'message' => 'Authorization header missing or invalid'], 401);
    }
    $token = $matches[1];

    // Get JSON data
    $data = json_decode(file_get_contents("php://input"), true);

    // Validate required fields
    if (empty($data['title']) || empty($data['amount']) || empty($data['date'])) {
        jsonResponse(['success' => false, 'message' => 'Title, amount and date are required'], 400);
    }

    try {
        // Get user by token
        $stmt = $pdo->prepare("SELECT id FROM users WHERE token = ?");
        $stmt->execute([$token]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            jsonResponse(['success' => false, 'message' => 'Invalid token'], 401);
        }

        $user_id = $user['id'];

        // Create expenses table if not exists
        $pdo->exec("CREATE TABLE IF NOT EXISTS expenses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id BIGINT UNSIGNED NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            category VARCHAR(100),
            type VARCHAR(100),
            lend TINYINT(1) DEFAULT 0,
            amount DECIMAL(10,2) NOT NULL,
            expense_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        ) ENGINE=InnoDB;");

        // Check if expense already exists
        $check_stmt = $pdo->prepare(
            "SELECT id FROM expenses 
            WHERE user_id = ? 
            AND title = ? 
            AND amount = ? 
            AND expense_date = ? 
            AND COALESCE(description,'') = COALESCE(?,'')"
        );

        $check_stmt->execute([
            $user_id,
            $data['title'],
            $data['amount'],
            $data['date'],
            $data['description'] ?? null
        ]);

        if ($check_stmt->rowCount() > 0) {
            jsonResponse(['success' => false, 'message' => 'Duplicate expense entry'], 400);
        }

        // Insert new expense
        $insert_stmt = $pdo->prepare(
            "INSERT INTO expenses 
            (user_id, title, description, category, type, lend, amount, expense_date) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        );

        $insert_stmt->execute([
            $user_id,
            $data['title'],
            $data['description'] ?? null,
            $data['category'] ?? null,
            $data['type'] ?? null,
            $data['lend'] ?? 0,
            $data['amount'],
            $data['date']
        ]);

        jsonResponse([
            'success' => true,
            'message' => 'Expense uploaded successfully',
            'expense_id' => $pdo->lastInsertId()
        ]);
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}

// ========== FETCH EXPENSES ==========
// GET /users.php?action=fetch-expenses
// Required: Authorization header with Bearer token
if ($action === 'fetch-expenses' && $requestMethod === 'GET') {
    // Get token from Authorization header
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

    if (empty($auth_header) || !preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
        jsonResponse(['success' => false, 'message' => 'Authorization header missing or invalid'], 401);
    }

    $token = $matches[1];

    try {
        // Get user by token
        $stmt = $pdo->prepare("SELECT id FROM users WHERE token = ?");
        $stmt->execute([$token]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            jsonResponse(['success' => false, 'message' => 'Invalid token'], 401);
        }

        $user_id = $user['id'];

        // Check if expenses table exists
        $table_check = $pdo->query("SHOW TABLES LIKE 'expenses'");
        if ($table_check->rowCount() == 0) {
            // No expenses table yet
            jsonResponse(['success' => true, 'expenses' => [], 'message' => 'No expenses found']);
        }

        // Fetch expenses for this user
        $expenses_stmt = $pdo->prepare("SELECT * FROM expenses WHERE user_id = ? ORDER BY expense_date DESC");
        $expenses_stmt->execute([$user_id]);
        $expenses = $expenses_stmt->fetchAll(PDO::FETCH_ASSOC);

        jsonResponse([
            'success' => true,
            'expenses' => $expenses
        ]);
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}

// ========== UPLOAD TODOS ==========
// POST /users.php?action=upload-todos
// Required: Authorization header with Bearer token, todo data
if ($action === 'upload-todos' && $requestMethod === 'POST') {
    // Get token from Authorization header
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

    if (empty($auth_header) || !preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
        jsonResponse(['success' => false, 'message' => 'Authorization header missing or invalid'], 401);
    }
    $token = $matches[1];

    // Get JSON data
    $data = json_decode(file_get_contents("php://input"), true);

    // Validate required fields
    if (empty($data['title']) || empty($data['date'])) {
        jsonResponse(['success' => false, 'message' => 'Title and date are required'], 400);
    }

    try {
        // Get user by token
        $stmt = $pdo->prepare("SELECT id FROM users WHERE token = ?");
        $stmt->execute([$token]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            jsonResponse(['success' => false, 'message' => 'Invalid token'], 401);
        }

        $user_id = $user['id'];

        // Create todos table if not exists
        $pdo->exec("CREATE TABLE IF NOT EXISTS todos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id BIGINT UNSIGNED NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            todo_date DATE NOT NULL,
            todo_time TIME,
            is_completed TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        ) ENGINE=InnoDB;");

        // Check if todo already exists
        $check_stmt = $pdo->prepare(
            "SELECT id FROM todos 
            WHERE user_id = ? 
            AND title = ? 
            AND todo_date = ? 
            AND COALESCE(description,'') = COALESCE(?,'')"
        );

        $check_stmt->execute([
            $user_id,
            $data['title'],
            $data['date'],
            $data['description'] ?? null
        ]);

        if ($check_stmt->rowCount() > 0) {
            jsonResponse(['success' => false, 'message' => 'Duplicate todo entry'], 400);
        }

        // Insert new todo
        $insert_stmt = $pdo->prepare(
            "INSERT INTO todos 
            (user_id, title, description, todo_date, todo_time, is_completed) 
            VALUES (?, ?, ?, ?, ?, ?)"
        );

        $insert_stmt->execute([
            $user_id,
            $data['title'],
            $data['description'] ?? null,
            $data['date'],
            $data['time'] ?? null,
            $data['is_completed'] ? 1 : 0
        ]);

        jsonResponse([
            'success' => true,
            'message' => 'Todo uploaded successfully',
            'todo_id' => $pdo->lastInsertId()
        ]);
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}

// ========== FETCH TODOS ==========
// GET /users.php?action=fetch-todos
// Required: Authorization header with Bearer token
if ($action === 'fetch-todos' && $requestMethod === 'GET') {
    // Get token from Authorization header
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

    if (empty($auth_header) || !preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
        jsonResponse(['success' => false, 'message' => 'Authorization header missing or invalid'], 401);
    }

    $token = $matches[1];

    try {
        // Get user by token
        $stmt = $pdo->prepare("SELECT id FROM users WHERE token = ?");
        $stmt->execute([$token]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            jsonResponse(['success' => false, 'message' => 'Invalid token'], 401);
        }

        $user_id = $user['id'];

        // Check if todos table exists
        $table_check = $pdo->query("SHOW TABLES LIKE 'todos'");
        if ($table_check->rowCount() == 0) {
            // No todos table yet
            jsonResponse(['success' => true, 'todos' => [], 'message' => 'No todos found']);
        }

        // Fetch todos for this user
        $todos_stmt = $pdo->prepare("SELECT * FROM todos WHERE user_id = ? ORDER BY todo_date DESC");
        $todos_stmt->execute([$user_id]);
        $todos = $todos_stmt->fetchAll(PDO::FETCH_ASSOC);

        jsonResponse([
            'success' => true,
            'todos' => $todos
        ]);
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}

// ========== Fallback ==========
jsonResponse(['success' => false, 'message' => 'Invalid endpoint or method'], 404);
// ======== INVALID REQUEST ==========
