<?php
// Show errors for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', '1');

include '../admin/connect.php';
include '../functions.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Build query for categories
$sql = "SELECT * FROM categories ORDER BY title ASC";
$result = mysqli_query($con, $sql);
$categories = [];

if (mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
        $categories[] = [
            'id' => $row['id'],
            'title' => $row['title'],
            'slug' => $row['slug'],
            'description' => $row['description'] ?? '',
        ];
    }
}

// Return JSON response
echo json_encode([
    'success' => true,
    'count' => count($categories),
    'categories' => $categories
]);