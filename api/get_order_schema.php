<?php
// Show errors for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', '1');

// Set headers for JSON response
header('Content-Type: application/json');

// Define the order schema
$order_schema = [
    'type' => 'object',
    'required' => ['customer_name', 'phone_primary', 'address', 'products'],
    'properties' => [
        'customer_name' => [
            'type' => 'string',
            'description' => 'Full name of the customer'
        ],
        'phone_primary' => [
            'type' => 'string',
            'description' => 'Primary contact phone number'
        ],
        'phone_alternate' => [
            'type' => 'string',
            'description' => 'Alternative phone number (optional)'
        ],
        'address' => [
            'type' => 'string',
            'description' => 'Delivery address'
        ],
        'platform' => [
            'type' => 'string',
            'description' => 'Order source platform (optional, defaults to "API")'
        ],
        'notes' => [
            'type' => 'string',
            'description' => 'Additional notes for the order (optional)'
        ],
        'products' => [
            'type' => 'array',
            'description' => 'Array of products to order',
            'items' => [
                'type' => 'object',
                'required' => ['id', 'quantity'],
                'properties' => [
                    'id' => [
                        'type' => 'integer',
                        'description' => 'Product ID'
                    ],
                    'quantity' => [
                        'type' => 'integer',
                        'description' => 'Quantity to order',
                        'minimum' => 1
                    ]
                ]
            ]
        ]
    ]
];

// Example POST request body (commented out as requested)
/*
$example_request = [
    'customer_name' => 'John Doe',
    'phone_primary' => '1234567890',
    'phone_alternate' => '0987654321',
    'address' => '123 Main St, Anytown, AN 12345',
    'platform' => 'Mobile App',
    'notes' => 'Please deliver in the evening',
    'products' => [
        [
            'id' => 1,
            'quantity' => 2
        ],
        [
            'id' => 3,
            'quantity' => 1
        ]
    ]
];
*/

// Return schema
echo json_encode([
    'success' => true,
    'schema' => $order_schema
    // 'example' => $example_request  // Commented out as requested
]);