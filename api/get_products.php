<?php
// Show errors for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', '1');

include '../admin/connect.php';
include '../functions.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Get website URL from settings
$website_url = get_setting($pdo, 'website_url');

// Get query parameters
$search_query = isset($_GET['search']) ? mysqli_real_escape_string($con, $_GET['search']) : '';
$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
$category_slug = isset($_GET['category_slug']) ? mysqli_real_escape_string($con, $_GET['category_slug']) : '';
$sort_by = isset($_GET['sort']) ? mysqli_real_escape_string($con, $_GET['sort']) : 'featured'; // Options: featured, price_low, price_high, newest
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 0; // 0 means no limit

// Build query for products
$sql = "SELECT p.*, c.title as category_name, c.slug as category_slug 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE 1=1";

// Add search condition if search query is provided
if (!empty($search_query)) {
    $sql .= " AND (p.name LIKE '%$search_query%' OR p.description LIKE '%$search_query%')";
}

// Add category condition if category is provided
if ($category_id > 0) {
    $sql .= " AND p.category_id = $category_id";
} elseif (!empty($category_slug)) {
    $sql .= " AND c.slug = '$category_slug'";
}

// Add sorting
switch ($sort_by) {
    case 'price_low':
        $sql .= " ORDER BY p.coin_price ASC";
        break;
    case 'price_high':
        $sql .= " ORDER BY p.coin_price DESC";
        break;
    case 'newest':
        $sql .= " ORDER BY p.id DESC";
        break;
    case 'featured':
    default:
        $sql .= " ORDER BY p.is_featured DESC, p.id DESC";
        break;
}

// Add limit if specified
if ($limit > 0) {
    $sql .= " LIMIT $limit";
}

$result = mysqli_query($con, $sql);
$products = [];

if (mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
        // Get product image with full URL
        $image_path = $row['image'] ? $website_url . "uploads/products/" . $row['image'] : 'assets/images/no.svg';
        
        // Format price
        $price = !empty($row['offer_price']) && $row['offer_price'] < $row['price'] ? $row['offer_price'] : $row['price'];
        
        // Get additional images
        $additional_images = [];
        $images_query = mysqli_query($con, "SELECT image_path FROM product_images WHERE product_id = '{$row['id']}'");
        while ($img = mysqli_fetch_assoc($images_query)) {
            $additional_images[] = $website_url . "uploads/products/" . $img['image_path'];
        }
        if ($row['is_featured']) {
               $products[] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'slug' => $row['slug'],
            'description' => $row['description'],
            'price' => $price,
            'original_price' => $row['price'],
            'coin_price' => $row['coin_price'],
            'in_stock' => (bool)$row['in_stock'],
            'is_featured' => (bool)$row['is_featured'],
            'image' => $image_path,
            'additional_images' => $additional_images,
            'category' => $row['category_name'],
            'category_slug' => $row['category_slug']
        ];
        }
     
    }
}

// Return JSON response
echo json_encode([
    'success' => true,
    'count' => count($products),
    'products' => $products
]);
