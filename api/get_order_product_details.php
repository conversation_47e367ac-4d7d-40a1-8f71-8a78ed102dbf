<?php
header('Content-Type: application/json');
include 'db_connect.php';
include 'auth_check.php';

// Check if user is authenticated
$user_id = checkAuth();
if (!$user_id) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Get order ID from request
$order_id = isset($_POST['order_id']) ? mysqli_real_escape_string($con, $_POST['order_id']) : '';

if (empty($order_id)) {
    echo json_encode(['success' => false, 'message' => 'Order ID is required']);
    exit;
}

// Verify the order belongs to the user
$order_check = mysqli_query($con, "SELECT * FROM orders WHERE id = '$order_id' AND user_id = '$user_id'");
if (mysqli_num_rows($order_check) == 0) {
    echo json_encode(['success' => false, 'message' => 'Order not found or does not belong to this user']);
    exit;
}

// Get product details from order_items and products tables
$query = "SELECT p.id, p.title as name, p.description, p.image 
          FROM order_items oi 
          JOIN products p ON oi.product_id = p.id 
          WHERE oi.order_id = '$order_id' 
          LIMIT 1";

$result = mysqli_query($con, $query);

if (mysqli_num_rows($result) > 0) {
    $product = mysqli_fetch_assoc($result);
    
    // Format image URL if needed
    if (!empty($product['image'])) {
        $product['image'] = $website_url . "uploads/products/" . $product['image'];
    }
    
    echo json_encode([
        'success' => true,
        'product' => $product
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Product details not found'
    ]);
}
?>