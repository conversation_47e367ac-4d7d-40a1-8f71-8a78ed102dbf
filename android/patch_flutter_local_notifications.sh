#!/bin/bash

# Path to the flutter_local_notifications plugin
PLUGIN_PATH="$HOME/.pub-cache/hosted/pub.dev/flutter_local_notifications-9.9.1/android"

# Check if the plugin directory exists
if [ ! -d "$PLUGIN_PATH" ]; then
  echo "Plugin directory not found at $PLUGIN_PATH"
  exit 1
fi

# Add namespace to build.gradle
if grep -q "namespace" "$PLUGIN_PATH/build.gradle"; then
  echo "Namespace already exists in build.gradle"
else
  # Create a temporary file
  TMP_FILE=$(mktemp)
  
  # Add namespace after android block starts
  awk '
  /android {/ {
    print $0
    print "    namespace \"com.dexterous.flutterlocalnotifications\""
    next
  }
  { print }
  ' "$PLUGIN_PATH/build.gradle" > "$TMP_FILE"
  
  # Replace the original file
  mv "$TMP_FILE" "$PLUGIN_PATH/build.gradle"
  
  echo "Added namespace to build.gradle"
fi

echo "Patching complete!"